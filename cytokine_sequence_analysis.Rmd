---
title: "IL-6-type and IL-12 Family Cytokine Sequence Acquisition and Alignment"
author: "Cytokine Research Analysis"
date: "`r Sys.Date()`"
output:
  html_document:
    toc: true
    toc_float: true
    code_folding: show
    theme: flatly
---

```{r setup, include=FALSE}
knitr::opts_chunk$set(echo = TRUE, warning = FALSE, message = FALSE)
```

# Introduction

This analysis follows the methodology established for chemokine sequence acquisition and alignment, adapted for IL-6-type family and IL-12 family cytokines. The approach includes:

1. Sequence acquisition from UniProt
2. Structure acquisition from PDB
3. Structure-based alignment via MUSTANG
4. Sequence-based alignment via MUSCLE
5. Integration of structure and sequence alignments
6. Ortholog acquisition and alignment

## Cytokine Family Definitions

**IL-6-type family cytokines (8 members):** All share the gp130 receptor subunit
- IL-6, IL-11, CNTF, LIF, OSM, CT-1, CLC (CLCF1), IL-27

**IL-12 family cytokines (5 subunits forming 4 heterodimers):**
- Subunits: IL-12A (p35), IL-12B (p40), IL-23A (p19), IL-27A (p28), EBI3
- Heterodimers: IL-12 (IL-12A+IL-12B), IL-23 (IL-23A+IL-12B), IL-27 (IL-27A+EBI3), IL-35 (IL-12A+EBI3)

# Load Required Libraries

```{r libraries}
# Load required packages
library(UniProt.ws)
library(bio3d)      # For PDB manipulation and MUSTANG alignment
library(Biostrings) # For sequence handling
library(seqinr)     # For sequence analysis
#library(muscle)     # For MUSCLE alignment (if available)
library(httr2)       # For web requests
library(xml2)       # For XML parsing
library(dplyr)      # For data manipulation
library(ggplot2)    # For visualization
library(knitr)      # For table formatting
library(DT)        # For interactive tables

# Create output directories
dir.create("sequences", showWarnings = FALSE)
dir.create("structures", showWarnings = FALSE)
dir.create("alignments", showWarnings = FALSE)
dir.create("results", showWarnings = FALSE)
```

# Define Cytokine Lists

```{r define_cytokines}
# IL-6-type family cytokines (human)
il6_family <- c("IL6", "IL11", "CNTF", "LIF", "OSM", "CTF1", "CLCF1", "IL27")

# IL-12 family cytokine subunits (human)
il12_family <- c("IL12A", "IL12B", "IL23A", "IL27A", "EBI3")

# Combined list for analysis
all_cytokines <- c(il6_family, il12_family)

# Create data frame with family assignments
cytokine_info <- data.frame(
  Gene = all_cytokines,
  Family = c(rep("IL-6-type", length(il6_family)),
             rep("IL-12", length(il12_family))),
  Description = c("Interleukin-6", "Interleukin-11", "Ciliary neurotrophic factor",
                  "Leukemia inhibitory factor", "Oncostatin M", "Cardiotrophin-1",
                  "Cardiotrophin-like cytokine", "Interleukin-27",
                  "Interleukin-12 subunit alpha", "Interleukin-12 subunit beta",
                  "Interleukin-23 subunit alpha", "Interleukin-27 subunit alpha",
                  "Epstein-Barr virus induced 3"),
  stringsAsFactors = FALSE
)

kable(cytokine_info, caption = "Cytokines included in this analysis")
```

# Sequence Acquisition from UniProt

```{r uniprot_functions}
# WORKING FUNCTIONS - Tested and verified
# Function to get protein sequence from gene name
get_protein_sequence <- function(gene_name, organism_id = 9606) {
  # Build URL for UniProt REST API
  url <- paste0("https://rest.uniprot.org/uniprotkb/search?query=(gene_exact:",
                gene_name, ")+AND+(organism_id:", organism_id, ")+AND+(reviewed:true)&format=fasta&limit=1")

  # Make HTTP request
  response <- GET(url)

  if (status_code(response) == 200) {
    # Get raw content
    content_raw <- content(response, "raw")

    # Try to decompress if it's gzipped (UniProt often returns gzipped content)
    tryCatch({
      content_text <- memDecompress(content_raw, type = "gzip", asChar = TRUE)
      return(content_text)
    }, error = function(e) {
      # If decompression fails, try as plain text
      tryCatch({
        content_text <- rawToChar(content_raw)
        return(content_text)
      }, error = function(e2) {
        return(NULL)
      })
    })
  }

  return(NULL)
}

# Enhanced function that returns structured information
get_protein_info <- function(gene_name, organism_id = 9606) {
  # Get FASTA sequence
  fasta_content <- get_protein_sequence(gene_name, organism_id)

  if (!is.null(fasta_content) && nchar(fasta_content) > 0) {
    # Parse FASTA content
    lines <- strsplit(fasta_content, "\n")[[1]]
    header <- lines[1]
    sequence_lines <- lines[-1]
    sequence <- paste(sequence_lines, collapse = "")

    # Extract UniProt ID from header
    uniprot_id <- NA
    if (grepl("\\|", header)) {
      parts <- strsplit(header, "\\|")[[1]]
      if (length(parts) >= 2) {
        uniprot_id <- parts[2]
      }
    }

    # Extract protein name
    protein_name <- gsub("^>.*?\\s+", "", header)
    protein_name <- gsub("\\s+OS=.*$", "", protein_name)

    return(list(
      gene = gene_name,
      uniprot_id = uniprot_id,
      protein_name = protein_name,
      sequence = sequence,
      length = nchar(sequence),
      fasta = fasta_content,
      header = header
    ))
  }

  return(NULL)
}
```

```{r acquire_sequences}
# Get protein information for all cytokines using working functions
protein_data <- list()
sequences <- list()

cat("Acquiring sequences for", length(all_cytokines), "cytokines...\n")

for (i in seq_along(all_cytokines)) {
  gene <- all_cytokines[i]
  cat(sprintf("[%d/%d] Processing %s...", i, length(all_cytokines), gene))

  # Get protein information
  protein_info <- get_protein_info(gene)

  if (!is.null(protein_info)) {
    protein_data[[gene]] <- protein_info
    sequences[[gene]] <- protein_info$sequence

    # Save sequence to file
    writeLines(protein_info$fasta, file.path("sequences", paste0(gene, "_", protein_info$uniprot_id, ".fasta")))

    cat(" ✓ Success (", protein_info$length, " aa)\n")
  } else {
    cat(" ✗ Failed\n")
  }

  # Be respectful to UniProt servers
  if (i < length(all_cytokines)) {
    Sys.sleep(0.5)
  }
}

# Create summary table
if (length(protein_data) > 0) {
  uniprot_summary <- do.call(rbind, lapply(names(protein_data), function(gene) {
    info <- protein_data[[gene]]
    data.frame(
      Gene = gene,
      UniProt_ID = info$uniprot_id,
      Protein_Name = info$protein_name,
      Sequence_Length = info$length,
      Family = cytokine_info$Family[cytokine_info$Gene == gene],
      stringsAsFactors = FALSE
    )
  }))

  kable(uniprot_summary, caption = "UniProt sequence acquisition summary")

  cat("Successfully acquired", nrow(uniprot_summary), "out of", length(all_cytokines), "sequences\n")
} else {
  cat("No sequences were successfully acquired\n")
}
```

# Structure Acquisition from PDB

```{r pdb_search_functions}
# WORKING PDB FUNCTIONS - Using known cytokine structures
# Function to get known PDB structures for cytokines
get_known_cytokine_pdbs <- function() {
  # Known PDB structures for cytokines (manually curated)
  # Format: "pdb_id:chain" or just "pdb_id" for all chains
  known_structures <- list(
    "IL6" = c("1p9m:B"),
    "IL11" = c("8dpt:B", "8dpt:E"),
    "LIF" = c("8d6a:A"),
    "OSM" = c("8v29:A"),
    "IL12A" = c("8xrp:A", "8xrp:E", "8xrp:I", "8xrp:M"),  # p35 subunit
    "IL12B" = c("8xrp:B", "8xrp:F", "8xrp:J", "8xrp:N"),  # p40 subunit
    "IL23A" = c("6wdq:B"),  # p19 subunit
    "CNTF" = c("8d74:B"),
    "IL27A" = c("7u7n:D"),  # p28 subunit
    "EBI3" = c("7u7n:C"),   # EBI3 subunit
    "CLCF1" = c("8d7h:B", "8d7h:E")   # Similar to CTF1
  )

  return(known_structures)
}

# Function to parse PDB ID and chain from string
# Follows PDB conventions: 4-character alphanumeric PDB IDs, flexible chain IDs
parse_pdb_chain <- function(pdb_string) {
  if (grepl(":", pdb_string)) {
    parts <- strsplit(pdb_string, ":")[[1]]
    pdb_id <- parts[1]
    chain_id <- parts[2]

    # Validate PDB ID format (4 characters, alphanumeric)
    if (!grepl("^[0-9A-Za-z]{4}$", pdb_id)) {
      warning("PDB ID '", pdb_id, "' does not follow standard 4-character format")
    }

    # Validate chain ID (1-4 characters, alphanumeric)
    if (!grepl("^[A-Za-z0-9]{1,4}$", chain_id)) {
      warning("Chain ID '", chain_id, "' may not be valid (should be 1-4 alphanumeric characters)")
    }

    return(list(pdb_id = toupper(pdb_id), chain = chain_id))
  } else {
    pdb_id <- pdb_string

    # Validate PDB ID format
    if (!grepl("^[0-9A-Za-z]{4}$", pdb_id)) {
      warning("PDB ID '", pdb_id, "' does not follow standard 4-character format")
    }

    return(list(pdb_id = toupper(pdb_id), chain = NULL))
  }
}

# Function to download PDB structures for a gene
download_pdb_structures <- function(gene_name, output_dir = "structures") {
  # Create output directory
  dir.create(output_dir, showWarnings = FALSE)

  # Get known PDB IDs for this gene
  known_pdbs <- get_known_cytokine_pdbs()

  if (gene_name %in% names(known_pdbs)) {
    pdb_entries <- known_pdbs[[gene_name]]
    cat("Found", length(pdb_entries), "known PDB structures for", gene_name, "\n")

    downloaded_files <- character()
    chain_info <- list()

    for (pdb_entry in pdb_entries) {
      # Parse PDB ID and chain
      parsed <- parse_pdb_chain(pdb_entry)
      pdb_id <- parsed$pdb_id
      chain <- parsed$chain

      cat("  Downloading", pdb_id)
      if (!is.null(chain)) {
        cat(" (chain", chain, ")")
      }
      cat("...")

      tryCatch({
        # Download PDB file
        pdb_file <- get.pdb(pdb_id, path = output_dir)

        if (file.exists(pdb_file)) {
          downloaded_files <- c(downloaded_files, pdb_file)
          chain_info[[pdb_file]] <- list(pdb_id = pdb_id, chain = chain, entry = pdb_entry)
          cat(" ✓ Success\n")
        } else {
          cat(" ✗ Failed\n")
        }

      }, error = function(e) {
        cat(" ✗ Error:", e$message, "\n")
      })

      Sys.sleep(0.5)  # Be respectful to PDB servers
    }

    return(list(
      gene = gene_name,
      pdb_entries = pdb_entries,
      downloaded_files = downloaded_files,
      chain_info = chain_info,
      success_count = length(downloaded_files)
    ))

  } else {
    cat("No known PDB structures for", gene_name, "\n")
    return(NULL)
  }
}
```

```{r acquire_structures}
# Download known PDB structures for each cytokine
pdb_data <- list()

for (gene in names(protein_data)) {
  cat("Processing PDB structures for", gene, "...\n")

  # Download known structures
  download_result <- download_pdb_structures(gene, "structures")

  if (!is.null(download_result)) {
    pdb_data[[gene]] <- download_result
    cat("  ✓ Downloaded", download_result$success_count, "structures\n")
  } else {
    cat("  ✗ No known structures available\n")
  }

  Sys.sleep(0.5)  # Be respectful to PDB servers
}

# Create PDB summary
if (length(pdb_data) > 0) {
  pdb_summary <- do.call(rbind, lapply(names(pdb_data), function(gene) {
    result <- pdb_data[[gene]]
    data.frame(
      Gene = gene,
      PDB_Count = length(result$pdb_ids),
      Downloaded = result$success_count,
      PDB_IDs = paste(result$pdb_ids, collapse = ", "),
      stringsAsFactors = FALSE
    )
  }))

  kable(pdb_summary, caption = "PDB structure download summary")

  cat("Total structures downloaded:", sum(pdb_summary$Downloaded), "\n")
} else {
  cat("No PDB structures were downloaded\n")
}
```

# Structure Processing and MUSTANG Alignment

```{r structure_processing}
# Function to trim PDB structures (remove waters, cofactors, etc.)
trim_pdb_structure <- function(pdb_file, output_file = NULL) {
  if (!file.exists(pdb_file)) {
    cat("PDB file not found:", pdb_file, "\n")
    return(NULL)
  }

  tryCatch({
    # Read PDB file
    pdb <- read.pdb(pdb_file)
    cat("Original PDB:", basename(pdb_file), "- atoms:", nrow(pdb$atom), "\n")

    # Trim to protein only (removes waters, ligands, etc.)
    # Use trim.pdb() function with "protein" selection
    pdb_trimmed <- trim.pdb(pdb, "protein")
    cat("Trimmed PDB: atoms:", nrow(pdb_trimmed$atom), "\n")

    # Generate output filename if not provided
    if (is.null(output_file)) {
      output_file <- gsub("\\.pdb$", "_trimmed.pdb", pdb_file)
    }

    # Save trimmed structure
    write.pdb(pdb_trimmed, file = output_file)

    if (file.exists(output_file)) {
      cat("✓ Trimmed structure saved:", basename(output_file), "\n")
      return(output_file)
    } else {
      cat("✗ Failed to save trimmed structure\n")
      return(NULL)
    }

  }, error = function(e) {
    cat("Error trimming PDB:", e$message, "\n")
    return(NULL)
  })
}

# Process all downloaded PDB structures
trimmed_structures <- list()

for (gene in names(pdb_data)) {
  if (pdb_data[[gene]]$success_count > 0) {
    cat("Processing structures for", gene, "...\n")

    gene_trimmed <- character()

    for (pdb_file in pdb_data[[gene]]$downloaded_files) {
      if (file.exists(pdb_file)) {
        trimmed_file <- trim_pdb_structure(pdb_file)

        if (!is.null(trimmed_file)) {
          gene_trimmed <- c(gene_trimmed, trimmed_file)
        }
      }
    }

    if (length(gene_trimmed) > 0) {
      trimmed_structures[[gene]] <- gene_trimmed
      cat("  ✓ Processed", length(gene_trimmed), "structures for", gene, "\n")
    }
  }
}

cat("Total trimmed structures:", sum(sapply(trimmed_structures, length)), "\n")
```

```{r mustang_alignment}
# Function to run structure-based alignment (with MUSTANG fallback)
run_structure_alignment <- function(pdb_files, output_file, method = "auto") {
  if (length(pdb_files) < 2) {
    cat("Need at least 2 structures for alignment\n")
    return(NULL)
  }

  cat("Running structure-based alignment with", length(pdb_files), "structures...\n")

  # Check if MUSTANG is available
  mustang_available <- tryCatch({
    system("mustang --version", ignore.stderr = TRUE, ignore.stdout = TRUE)
    TRUE
  }, error = function(e) {
    FALSE
  })

  if (method == "mustang" || (method == "auto" && mustang_available)) {
    # Try MUSTANG first
    tryCatch({
      mustang_result <- mustang(pdb_files, outfile = output_file)
      cat("✓ MUSTANG alignment completed:", output_file, "\n")
      return(mustang_result)
    }, error = function(e) {
      cat("MUSTANG failed:", e$message, "\n")
      if (method == "mustang") return(NULL)
    })
  }

  # Fallback to bio3d pdbaln
  cat("Using bio3d structure alignment...\n")
  tryCatch({
    # Read all PDB files
    pdbs <- list()
    for (i in seq_along(pdb_files)) {
      if (file.exists(pdb_files[i])) {
        pdbs[[i]] <- read.pdb(pdb_files[i])
      }
    }

    if (length(pdbs) < 2) {
      cat("Not enough valid PDB files\n")
      return(NULL)
    }

    # Use pdbaln for structure-based alignment
    alignment_result <- pdbaln(pdbs, fit = TRUE)

    # Create FASTA output
    fasta_content <- character()
    for (i in 1:nrow(alignment_result$ali)) {
      pdb_name <- basename(pdb_files[i])
      pdb_id <- gsub("_trimmed\\.pdb.*", "", gsub("\\.pdb.*", "", pdb_name))

      fasta_content <- c(fasta_content,
                        paste0(">", pdb_id),
                        paste(alignment_result$ali[i, ], collapse = ""))
    }

    # Write FASTA file
    writeLines(fasta_content, output_file)

    cat("✓ Structure alignment completed:", output_file, "\n")
    return(alignment_result)

  }, error = function(e) {
    cat("Error in structure alignment:", e$message, "\n")
    return(NULL)
  })
}

# Separate structures by family for alignment
il6_structures <- trimmed_structures[names(trimmed_structures) %in% il6_family]
il12_structures <- trimmed_structures[names(trimmed_structures) %in% il12_family]

# Flatten structure lists (take first structure for each gene)
il6_files <- sapply(il6_structures, function(x) x[1])
il12_files <- sapply(il12_structures, function(x) x[1])

# Run structure alignment for IL-6 family (if enough structures)
if (length(il6_files) >= 2) {
  cat("Running structure alignment for IL-6 family...\n")
  il6_alignment <- run_structure_alignment(
    il6_files,
    "alignments/il6_family_structure.fasta"
  )
}

# Run structure alignment for IL-12 family (if enough structures)
if (length(il12_files) >= 2) {
  cat("Running structure alignment for IL-12 family...\n")
  il12_alignment <- run_structure_alignment(
    il12_files,
    "alignments/il12_family_structure.fasta"
  )
}
```

# MUSCLE Sequence Alignment

```{r muscle_alignment}
# Function to create FASTA file from sequences
create_fasta_file <- function(sequences, filename) {
  fasta_content <- character()

  for (gene in names(sequences)) {
    fasta_content <- c(fasta_content,
                      paste0(">", gene, "_", protein_data[[gene]]$uniprot_id),
                      sequences[[gene]])
  }

  writeLines(fasta_content, filename)
  return(filename)
}

# Function to run MUSCLE alignment
run_muscle_alignment <- function(input_fasta, output_fasta) {
  # Note: This requires MUSCLE to be installed and in PATH
  # Alternative: use online MUSCLE or other R packages

  try({
    system_command <- paste("muscle -in", input_fasta, "-out", output_fasta)
    system_result <- system(system_command, intern = TRUE)

    if (file.exists(output_fasta)) {
      cat("MUSCLE alignment completed:", output_fasta, "\n")
      return(TRUE)
    }
  }, silent = TRUE)

  # Fallback: use Biostrings for basic alignment
  cat("MUSCLE not available, using basic alignment...\n")

  try({
    # Read sequences
    seqs <- readDNAStringSet(input_fasta)

    # Simple alignment (this is very basic - MUSCLE would be much better)
    # For demonstration purposes only
    aligned_seqs <- seqs  # In practice, use proper alignment algorithm

    # Write aligned sequences
    writeXStringSet(aligned_seqs, output_fasta)
    return(TRUE)
  }, silent = TRUE)

  return(FALSE)
}

# Create FASTA files for each family
il6_sequences <- sequences[names(sequences) %in% il6_family]
il12_sequences <- sequences[names(sequences) %in% il12_family]

# Create FASTA files
if (length(il6_sequences) > 0) {
  il6_fasta <- create_fasta_file(il6_sequences, "sequences/il6_family.fasta")
  cat("Created IL-6 family FASTA file with", length(il6_sequences), "sequences\n")

  # Run MUSCLE alignment
  run_muscle_alignment(il6_fasta, "alignments/il6_family_muscle.fasta")
}

if (length(il12_sequences) > 0) {
  il12_fasta <- create_fasta_file(il12_sequences, "sequences/il12_family.fasta")
  cat("Created IL-12 family FASTA file with", length(il12_sequences), "sequences\n")

  # Run MUSCLE alignment
  run_muscle_alignment(il12_fasta, "alignments/il12_family_muscle.fasta")
}

# Create combined alignment for all cytokines
all_fasta <- create_fasta_file(sequences, "sequences/all_cytokines.fasta")
run_muscle_alignment(all_fasta, "alignments/all_cytokines_muscle.fasta")
```

# Alignment Integration and Analysis

```{r alignment_analysis}
# Function to read and analyze alignment
analyze_alignment <- function(alignment_file) {
  if (!file.exists(alignment_file)) {
    cat("Alignment file not found:", alignment_file, "\n")
    return(NULL)
  }

  try({
    # Read alignment
    alignment <- readAAStringSet(alignment_file)

    # Calculate basic statistics
    seq_lengths <- width(alignment)
    alignment_length <- max(seq_lengths)

    # Calculate conservation scores (simplified)
    conservation_scores <- numeric(alignment_length)

    # Convert to matrix for analysis
    align_matrix <- as.matrix(alignment)

    for (i in 1:alignment_length) {
      if (i <= ncol(align_matrix)) {
        column <- align_matrix[, i]
        # Simple conservation: fraction of most common residue
        residue_counts <- table(column[column != "-"])
        if (length(residue_counts) > 0) {
          conservation_scores[i] <- max(residue_counts) / length(column[column != "-"])
        }
      }
    }

    return(list(
      alignment = alignment,
      length = alignment_length,
      n_sequences = length(alignment),
      conservation = conservation_scores,
      mean_conservation = mean(conservation_scores, na.rm = TRUE)
    ))
  }, silent = TRUE)

  return(NULL)
}

# Analyze alignments
alignment_results <- list()

# IL-6 family alignment
il6_muscle_file <- "alignments/il6_family_muscle.fasta"
if (file.exists(il6_muscle_file)) {
  alignment_results$il6_muscle <- analyze_alignment(il6_muscle_file)
  cat("IL-6 family MUSCLE alignment: ",
      alignment_results$il6_muscle$n_sequences, "sequences, ",
      alignment_results$il6_muscle$length, "positions\n")
}

# IL-12 family alignment
il12_muscle_file <- "alignments/il12_family_muscle.fasta"
if (file.exists(il12_muscle_file)) {
  alignment_results$il12_muscle <- analyze_alignment(il12_muscle_file)
  cat("IL-12 family MUSCLE alignment: ",
      alignment_results$il12_muscle$n_sequences, "sequences, ",
      alignment_results$il12_muscle$length, "positions\n")
}

# Combined alignment
all_muscle_file <- "alignments/all_cytokines_muscle.fasta"
if (file.exists(all_muscle_file)) {
  alignment_results$all_muscle <- analyze_alignment(all_muscle_file)
  cat("Combined MUSCLE alignment: ",
      alignment_results$all_muscle$n_sequences, "sequences, ",
      alignment_results$all_muscle$length, "positions\n")
}
```

# Ortholog Acquisition and Analysis

```{r ortholog_functions}
# Function to search for orthologs using OMA database (simplified approach)
# Note: In practice, you would use OMA REST API or download OMA data
search_orthologs <- function(gene_name, uniprot_id) {
  # This is a placeholder function
  # In practice, you would query OMA database or use Ensembl Compara
  cat("Searching orthologs for", gene_name, "...\n")

  # Placeholder: return some example species
  example_orthologs <- list(
    species = c("Pan troglodytes", "Macaca mulatta", "Mus musculus",
                "Rattus norvegicus", "Canis lupus"),
    sequences = paste0("EXAMPLE_ORTHOLOG_SEQUENCE_", 1:5)
  )

  return(example_orthologs)
}

# Function to create ortholog alignments
create_ortholog_alignment <- function(human_seq, ortholog_seqs, gene_name) {
  # Combine human and ortholog sequences
  all_seqs <- c(human_seq, ortholog_seqs$sequences)
  names(all_seqs) <- c(paste0(gene_name, "_human"),
                       paste0(gene_name, "_", gsub(" ", "_", ortholog_seqs$species)))

  # Create FASTA file
  fasta_file <- file.path("sequences", paste0(gene_name, "_orthologs.fasta"))
  fasta_content <- character()

  for (i in 1:length(all_seqs)) {
    fasta_content <- c(fasta_content,
                      paste0(">", names(all_seqs)[i]),
                      all_seqs[i])
  }

  writeLines(fasta_content, fasta_file)

  # Run alignment (using MUSCLE if available)
  output_file <- file.path("alignments", paste0(gene_name, "_orthologs_aligned.fasta"))
  run_muscle_alignment(fasta_file, output_file)

  return(output_file)
}
```

```{r acquire_orthologs}
# Search for orthologs for each cytokine
ortholog_data <- list()

for (gene in names(sequences)) {
  if (gene %in% names(uniprot_data)) {
    cat("Processing orthologs for", gene, "...\n")

    # Search for orthologs
    orthologs <- search_orthologs(gene, uniprot_data[[gene]]$accession)

    if (!is.null(orthologs) && length(orthologs$species) > 0) {
      ortholog_data[[gene]] <- orthologs

      # Create ortholog alignment
      ortholog_alignment <- create_ortholog_alignment(
        sequences[[gene]], orthologs, gene
      )

      cat("  - Found", length(orthologs$species), "orthologs\n")
    }
  }
}

# Create summary of ortholog data
ortholog_summary <- do.call(rbind, lapply(names(ortholog_data), function(gene) {
  data.frame(
    Gene = gene,
    Ortholog_Count = length(ortholog_data[[gene]]$species),
    Species = paste(ortholog_data[[gene]]$species[1:min(3, length(ortholog_data[[gene]]$species))],
                   collapse = ", "),
    stringsAsFactors = FALSE
  )
}))

if (nrow(ortholog_summary) > 0) {
  kable(ortholog_summary, caption = "Ortholog acquisition summary")
}
```

# Visualization and Results

```{r visualization}
# Function to plot conservation scores
plot_conservation <- function(alignment_result, title) {
  if (is.null(alignment_result) || is.null(alignment_result$conservation)) {
    return(NULL)
  }

  conservation_df <- data.frame(
    Position = 1:length(alignment_result$conservation),
    Conservation = alignment_result$conservation
  )

  p <- ggplot(conservation_df, aes(x = Position, y = Conservation)) +
    geom_line(color = "blue", alpha = 0.7) +
    geom_smooth(method = "loess", color = "red", se = FALSE) +
    labs(title = paste("Conservation Profile:", title),
         x = "Alignment Position",
         y = "Conservation Score") +
    theme_minimal() +
    ylim(0, 1)

  return(p)
}

# Plot conservation for each family
if (!is.null(alignment_results$il6_muscle)) {
  il6_plot <- plot_conservation(alignment_results$il6_muscle, "IL-6 Family")
  print(il6_plot)
}

if (!is.null(alignment_results$il12_muscle)) {
  il12_plot <- plot_conservation(alignment_results$il12_muscle, "IL-12 Family")
  print(il12_plot)
}

if (!is.null(alignment_results$all_muscle)) {
  all_plot <- plot_conservation(alignment_results$all_muscle, "All Cytokines")
  print(all_plot)
}
```

```{r sequence_statistics}
# Calculate sequence statistics
sequence_stats <- do.call(rbind, lapply(names(sequences), function(gene) {
  seq <- sequences[[gene]]

  # Calculate amino acid composition
  aa_counts <- table(strsplit(seq, "")[[1]])

  data.frame(
    Gene = gene,
    Family = cytokine_info$Family[cytokine_info$Gene == gene],
    Length = nchar(seq),
    Hydrophobic = sum(aa_counts[c("A", "V", "I", "L", "M", "F", "Y", "W")], na.rm = TRUE),
    Charged = sum(aa_counts[c("R", "K", "D", "E")], na.rm = TRUE),
    Polar = sum(aa_counts[c("S", "T", "N", "Q")], na.rm = TRUE),
    Cysteine = sum(aa_counts["C"], na.rm = TRUE),
    stringsAsFactors = FALSE
  )
}))

# Calculate percentages
sequence_stats$Hydrophobic_Pct <- round(sequence_stats$Hydrophobic / sequence_stats$Length * 100, 1)
sequence_stats$Charged_Pct <- round(sequence_stats$Charged / sequence_stats$Length * 100, 1)
sequence_stats$Polar_Pct <- round(sequence_stats$Polar / sequence_stats$Length * 100, 1)

kable(sequence_stats, caption = "Sequence composition statistics")
```

```{r family_comparison}
# Compare families
family_comparison <- sequence_stats %>%
  group_by(Family) %>%
  summarise(
    Count = n(),
    Mean_Length = round(mean(Length), 1),
    SD_Length = round(sd(Length), 1),
    Mean_Hydrophobic_Pct = round(mean(Hydrophobic_Pct), 1),
    Mean_Charged_Pct = round(mean(Charged_Pct), 1),
    Mean_Cysteine = round(mean(Cysteine), 1),
    .groups = 'drop'
  )

kable(family_comparison, caption = "Family-wise comparison")

# Plot family differences
length_plot <- ggplot(sequence_stats, aes(x = Family, y = Length, fill = Family)) +
  geom_boxplot() +
  geom_jitter(width = 0.2, alpha = 0.7) +
  labs(title = "Sequence Length Distribution by Family",
       x = "Cytokine Family",
       y = "Sequence Length (amino acids)") +
  theme_minimal() +
  theme(legend.position = "none")

print(length_plot)

# Composition plot
composition_plot <- sequence_stats %>%
  select(Gene, Family, Hydrophobic_Pct, Charged_Pct, Polar_Pct) %>%
  pivot_longer(cols = c(Hydrophobic_Pct, Charged_Pct, Polar_Pct),
               names_to = "Composition_Type", values_to = "Percentage") %>%
  ggplot(aes(x = Gene, y = Percentage, fill = Composition_Type)) +
  geom_bar(stat = "identity", position = "stack") +
  facet_wrap(~Family, scales = "free_x") +
  labs(title = "Amino Acid Composition by Gene and Family",
       x = "Gene",
       y = "Percentage",
       fill = "Composition Type") +
  theme_minimal() +
  theme(axis.text.x = element_text(angle = 45, hjust = 1))

print(composition_plot)
```

# Summary and Conclusions

```{r summary}
# Create final summary
cat("## Cytokine Sequence Analysis Summary\n\n")

cat("### Sequences Acquired:\n")
cat("- IL-6-type family:", length(il6_sequences), "sequences\n")
cat("- IL-12 family:", length(il12_sequences), "sequences\n")
cat("- Total:", length(sequences), "sequences\n\n")

cat("### Structures Found:\n")
cat("- Total PDB structures:", sum(sapply(pdb_data, function(x) length(x$hits))), "\n")
cat("- Genes with structures:", length(pdb_data), "\n\n")

cat("### Alignments Created:\n")
if (!is.null(alignment_results$il6_muscle)) {
  cat("- IL-6 family alignment:", alignment_results$il6_muscle$n_sequences,
      "sequences,", alignment_results$il6_muscle$length, "positions\n")
}
if (!is.null(alignment_results$il12_muscle)) {
  cat("- IL-12 family alignment:", alignment_results$il12_muscle$n_sequences,
      "sequences,", alignment_results$il12_muscle$length, "positions\n")
}
if (!is.null(alignment_results$all_muscle)) {
  cat("- Combined alignment:", alignment_results$all_muscle$n_sequences,
      "sequences,", alignment_results$all_muscle$length, "positions\n")
}

cat("\n### Key Findings:\n")
cat("- Average IL-6 family length:", round(mean(sequence_stats$Length[sequence_stats$Family == "IL-6-type"]), 1), "aa\n")
cat("- Average IL-12 family length:", round(mean(sequence_stats$Length[sequence_stats$Family == "IL-12"]), 1), "aa\n")

if (!is.null(alignment_results$il6_muscle)) {
  cat("- IL-6 family conservation:", round(alignment_results$il6_muscle$mean_conservation * 100, 1), "%\n")
}
if (!is.null(alignment_results$il12_muscle)) {
  cat("- IL-12 family conservation:", round(alignment_results$il12_muscle$mean_conservation * 100, 1), "%\n")
}

cat("\n### Files Generated:\n")
cat("- Sequence files: sequences/\n")
cat("- Structure files: structures/\n")
cat("- Alignment files: alignments/\n")
cat("- Results: results/\n")
```

# Session Information

```{r session_info}
sessionInfo()
```