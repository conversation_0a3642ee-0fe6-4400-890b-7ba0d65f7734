# Test script for the MUSTANG alignment part of structure_processing chunk
library(bio3d)

# Create test directories
dir.create("alignments", showWarnings = FALSE)

# Define cytokine families
il6_family <- c("IL6", "IL11", "CNTF", "LIF", "OSM", "CTF1", "CLCF1", "IL27")
il12_family <- c("IL12A", "IL12B", "IL23A", "IL27A", "EBI3")

# Function to run structure-based alignment (from the chunk)
run_structure_alignment <- function(pdb_files, output_file, method = "auto") {
  if (length(pdb_files) < 2) {
    cat("Need at least 2 structures for alignment\n")
    return(NULL)
  }

  cat("Running structure-based alignment with", length(pdb_files), "structures...\n")

  # Check if MUSTANG is available
  mustang_available <- tryCatch({
    system("mustang --version", ignore.stderr = TRUE, ignore.stdout = TRUE)
    TRUE
  }, error = function(e) {
    FALSE
  })

  if (method == "mustang" || (method == "auto" && mustang_available)) {
    # Try MUSTANG first
    tryCatch({
      mustang_result <- mustang(pdb_files, outfile = output_file)
      cat("✓ MUSTANG alignment completed:", output_file, "\n")
      return(mustang_result)
    }, error = function(e) {
      cat("MUSTANG failed:", e$message, "\n")
      if (method == "mustang") return(NULL)
    })
  }

  # Fallback to bio3d pdbaln
  cat("Using bio3d structure alignment...\n")
  tryCatch({
    # Read all PDB files
    pdbs <- list()
    for (i in seq_along(pdb_files)) {
      if (file.exists(pdb_files[i])) {
        pdbs[[i]] <- read.pdb(pdb_files[i])
      }
    }

    if (length(pdbs) < 2) {
      cat("Not enough valid PDB files\n")
      return(NULL)
    }

    # Use pdbaln for structure-based alignment
    alignment_result <- pdbaln(pdbs, fit = TRUE)

    # Create FASTA output
    fasta_content <- character()
    for (i in 1:nrow(alignment_result$ali)) {
      pdb_name <- basename(pdb_files[i])
      pdb_id <- gsub("_trimmed\\.pdb.*", "", gsub("\\.pdb.*", "", pdb_name))

      fasta_content <- c(fasta_content,
                        paste0(">", pdb_id),
                        paste(alignment_result$ali[i, ], collapse = ""))
    }

    # Write FASTA file
    writeLines(fasta_content, output_file)

    cat("✓ Structure alignment completed:", output_file, "\n")
    return(alignment_result)

  }, error = function(e) {
    cat("Error in structure alignment:", e$message, "\n")
    return(NULL)
  })
}

# Get available trimmed structures
trimmed_files <- list.files("structures", pattern = "_trimmed\\.pdb$", full.names = TRUE)

cat("Testing structure alignment functionality...\n")
cat("Available trimmed structures:", length(trimmed_files), "\n")

# Map files to genes (simplified mapping)
gene_mapping <- list(
  "IL6" = "1p9m_trimmed.pdb",
  "IL11" = "8DPT_trimmed.pdb", 
  "LIF" = "8D6A_trimmed.pdb",
  "OSM" = "8V29_trimmed.pdb",
  "CNTF" = "8D74_trimmed.pdb",
  "CLCF1" = "8D7H_trimmed.pdb",
  "IL23A" = "6WDQ_trimmed.pdb",
  "IL27A" = "7U7N_trimmed.pdb"
)

# Create structure lists by family
il6_structures <- list()
il12_structures <- list()

for (gene in names(gene_mapping)) {
  file_path <- file.path("structures", gene_mapping[[gene]])
  if (file.exists(file_path)) {
    if (gene %in% il6_family) {
      il6_structures[[gene]] <- file_path
    } else if (gene %in% il12_family) {
      il12_structures[[gene]] <- file_path
    }
  }
}

# Flatten structure lists (take first structure for each gene)
il6_files <- sapply(il6_structures, function(x) x[1])
il12_files <- sapply(il12_structures, function(x) x[1])

cat("\nTesting IL-6 family alignment...\n")
cat("IL-6 family files:", length(il6_files), "\n")
for (i in seq_along(il6_files)) {
  cat("  ", names(il6_files)[i], ":", basename(il6_files[i]), "\n")
}

# Run structure alignment for IL-6 family (if enough structures)
if (length(il6_files) >= 2) {
  cat("Running structure alignment for IL-6 family...\n")
  il6_alignment <- run_structure_alignment(
    il6_files,
    "alignments/il6_family_structure.fasta"
  )
  
  if (!is.null(il6_alignment)) {
    cat("IL-6 alignment successful!\n")
  }
} else {
  cat("Not enough IL-6 structures for alignment\n")
}

cat("\nTesting IL-12 family alignment...\n")
cat("IL-12 family files:", length(il12_files), "\n")
for (i in seq_along(il12_files)) {
  cat("  ", names(il12_files)[i], ":", basename(il12_files[i]), "\n")
}

# Run structure alignment for IL-12 family (if enough structures)
if (length(il12_files) >= 2) {
  cat("Running structure alignment for IL-12 family...\n")
  il12_alignment <- run_structure_alignment(
    il12_files,
    "alignments/il12_family_structure.fasta"
  )
  
  if (!is.null(il12_alignment)) {
    cat("IL-12 alignment successful!\n")
  }
} else {
  cat("Not enough IL-12 structures for alignment\n")
}

# Check output files
cat("\nChecking output files...\n")
if (file.exists("alignments/il6_family_structure.fasta")) {
  cat("✓ IL-6 family alignment file created\n")
  lines <- readLines("alignments/il6_family_structure.fasta")
  cat("  File contains", length(lines), "lines\n")
}

if (file.exists("alignments/il12_family_structure.fasta")) {
  cat("✓ IL-12 family alignment file created\n")
  lines <- readLines("alignments/il12_family_structure.fasta")
  cat("  File contains", length(lines), "lines\n")
}

cat("\nStructure alignment test completed!\n")
