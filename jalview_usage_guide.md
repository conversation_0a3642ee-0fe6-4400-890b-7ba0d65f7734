# Jalview Usage Guide for Cytokine Structure Alignments

## Installation Complete ✅
- **Jalview ********** installed via Homebrew
- Location: `/Applications/Jalview.app` and `/usr/local/bin/jalview`
- Ready to view your MUSTANG and MUSCLE alignments

## Opening Alignment Files

### Command Line Method:
```bash
# Navigate to your project directory
cd /Users/<USER>/obsidian/Eden/research/binder/cytokine-receptor-encoding/sequences

# Open specific alignment file
jalview alignments/quick_test_mustang.fasta

# Open multiple files
jalview alignments/*.fasta
```

### GUI Method:
1. Open Jalview application from Applications folder
2. File → Open File
3. Navigate to your `alignments/` directory
4. Select FASTA files

## Your Available Alignment Files

Currently available:
- `alignments/quick_test_mustang.fasta` - MUSTANG structural alignment (IL6 vs LIF)

Future files from your R analysis:
- `alignments/il6_family_structure.fasta` - IL-6 family structural alignment
- `alignments/il12_family_structure.fasta` - IL-12 family structural alignment
- `alignments/il6_family_muscle.fasta` - IL-6 family sequence alignment
- `alignments/il12_family_muscle.fasta` - IL-12 family sequence alignment

## Key Jalview Features for Structure Analysis

### 1. Viewing Alignments
- **Color schemes**: Use "Clustal" or "Percentage Identity" for conservation
- **Conservation**: View → Show Conservation to see conserved regions
- **Quality**: View → Show Quality for alignment quality scores

### 2. Structure Analysis Features
- **Secondary structure**: If available in your PDB files
- **3D structure viewer**: Structure → View PDB Structure (if PDB files linked)
- **Conservation coloring**: Highlight conserved structural regions

### 3. Useful Views for Cytokine Analysis
- **Overview window**: Shows entire alignment at once
- **Ruler**: Shows position numbers
- **Consensus**: Shows most common residue at each position
- **Logo**: Sequence logo showing conservation

### 4. Export Options
- **Images**: File → Export Image (PNG, SVG, EPS)
- **Alignments**: File → Export Alignment (various formats)
- **Statistics**: Calculate → Tree or PCA Plot

## Analyzing Your MUSTANG Alignment

The current test file shows:
- **2 structures**: IL6 (1p9m) and LIF (8D6A)
- **Structural alignment**: Based on 3D structure similarity
- **Gaps**: Represent structural differences between proteins

### What to Look For:
1. **Conserved regions**: Areas with few gaps and similar residues
2. **Structural motifs**: Common secondary structure elements
3. **Family-specific patterns**: Regions unique to cytokine families
4. **Active sites**: Known functional regions (if annotated)

## Tips for Cytokine Analysis

### 1. Color Schemes
- **Clustal**: Good for general viewing
- **Percentage Identity**: Shows conservation levels
- **Hydrophobicity**: Useful for membrane-binding regions
- **Charge**: Important for receptor interactions

### 2. Filtering and Editing
- **Remove gaps**: Edit → Remove Empty Columns
- **Select regions**: Click and drag to select specific areas
- **Group sequences**: Select → Group by family

### 3. Comparison Analysis
- **Calculate trees**: Calculate → Calculate Tree (for phylogenetic analysis)
- **PCA plots**: Calculate → Principal Component Analysis
- **Conservation plots**: Analyse → Conservation

## Workflow for Your Cytokine Project

1. **Open MUSTANG structural alignments** first
   - Look for structurally conserved regions
   - Identify family-specific structural features

2. **Compare with MUSCLE sequence alignments**
   - See how sequence conservation relates to structure
   - Identify regions where structure is conserved but sequence varies

3. **Analyze by family**
   - IL-6 family: Look for gp130-binding regions
   - IL-12 family: Look for heterodimer interfaces

4. **Export results**
   - Save high-quality images for publications
   - Export conservation data for further analysis

## Troubleshooting

### If Jalview doesn't open:
```bash
# Try opening the app directly
open /Applications/Jalview.app

# Or check Java version
java -version
```

### If alignment looks wrong:
- Check file format (should be FASTA)
- Verify alignment was generated correctly
- Try different color schemes

### Performance tips:
- For large alignments, use Overview window
- Close unused windows to save memory
- Use "Fast" rendering for large datasets

## Next Steps

1. **View current test alignment** in Jalview
2. **Run full cytokine analysis** to generate family alignments
3. **Compare structural vs sequence alignments**
4. **Export publication-quality figures**

The Jalview window should now be open with your MUSTANG alignment loaded!
