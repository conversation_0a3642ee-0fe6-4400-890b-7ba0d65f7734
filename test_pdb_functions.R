# Test Script for PDB Structure Functions
# Run this to verify the PDB functions work before running the full R Markdown

# Load required libraries
library(bio3d)

# Source the working functions
source("working_pdb_functions.R")

cat("=== Testing PDB Structure Functions ===\n\n")

# Test 1: Check known cytokine PDBs
cat("1. Testing known cytokine PDB list:\n")
known_pdbs <- get_known_cytokine_pdbs()
cat("Known cytokines with PDB structures:\n")
for (gene in names(known_pdbs)) {
  cat("  ", gene, ":", paste(known_pdbs[[gene]], collapse = ", "), "\n")
}
cat("\n")

# Test 2: Download structures for a few genes (using manually curated PDB accessions)
test_genes <- c("IL6", "IL12A")
cat("2. Testing PDB download for:", paste(test_genes, collapse = ", "), "\n")
cat("   IL6 should use PDB 1p9m:B\n")
cat("   IL12A should use PDB 8xrp with multiple chains (A,E,I,M)\n")

# Create test directory
test_dir <- "test_structures"
dir.create(test_dir, showWarnings = FALSE)

download_results <- list()
for (gene in test_genes) {
  cat("\n--- Testing", gene, "---\n")
  result <- download_pdb_structures(gene, test_dir)

  if (!is.null(result)) {
    download_results[[gene]] <- result
    cat("✓ Downloaded", result$success_count, "structures for", gene, "\n")
  } else {
    cat("✗ No structures downloaded for", gene, "\n")
  }
}

# Test 3: Trim structures
cat("\n3. Testing PDB trimming:\n")
trimmed_files <- character()

for (gene in names(download_results)) {
  result <- download_results[[gene]]

  if (result$success_count > 0) {
    # Test trimming the first downloaded file
    pdb_file <- result$downloaded_files[1]
    cat("Trimming", basename(pdb_file), "...\n")

    trimmed_file <- trim_pdb_structure(pdb_file)

    if (!is.null(trimmed_file)) {
      trimmed_files <- c(trimmed_files, trimmed_file)
      cat("✓ Trimming successful\n")
    } else {
      cat("✗ Trimming failed\n")
    }
  }
}

# Test 4: Structure alignment (if we have enough structures)
cat("\n4. Testing structure alignment:\n")

if (length(trimmed_files) >= 2) {
  cat("Running structure alignment with", length(trimmed_files), "structures...\n")

  alignment_result <- run_structure_alignment(
    trimmed_files[1:2],  # Use first 2 structures
    file.path(test_dir, "test_alignment.fasta")
  )

  if (!is.null(alignment_result)) {
    cat("✓ Structure alignment successful\n")

    # Check output file
    output_file <- file.path(test_dir, "test_alignment.fasta")
    if (file.exists(output_file)) {
      cat("✓ Alignment file created:", output_file, "\n")
      cat("File size:", file.size(output_file), "bytes\n")

      # Show first few lines
      cat("First few lines of alignment:\n")
      lines <- readLines(output_file, n = 6)
      for (line in lines) {
        cat("  ", line, "\n")
      }
    }
  } else {
    cat("✗ Structure alignment failed\n")
  }
} else {
  cat("Not enough structures for alignment test (need at least 2)\n")
}

# Test 5: Batch processing
cat("\n5. Testing batch processing:\n")
batch_genes <- c("IL6", "LIF")

batch_results <- batch_process_structures(batch_genes, test_dir)

if (length(batch_results) > 0) {
  cat("✓ Batch processing successful\n")

  # Create summary
  summary_table <- create_pdb_summary(batch_results)
  cat("Summary:\n")
  print(summary_table)
} else {
  cat("✗ Batch processing failed\n")
}

# Summary
cat("\n=== Test Summary ===\n")
cat("Known cytokine families:", length(known_pdbs), "\n")
cat("Genes tested:", length(test_genes), "\n")
cat("Structures downloaded:", sum(sapply(download_results, function(x) x$success_count)), "\n")
cat("Structures trimmed:", length(trimmed_files), "\n")

if (length(trimmed_files) >= 2) {
  cat("Structure alignment: Tested\n")
} else {
  cat("Structure alignment: Skipped (not enough structures)\n")
}

cat("\nTest files created in:", test_dir, "\n")
cat("You can delete the test directory if not needed:\n")
cat("  unlink('", test_dir, "', recursive = TRUE)\n", sep = "")

cat("\n=== Test Complete ===\n")
