# PDB Convention Compliance

This document outlines how our cytokine sequence analysis code aligns with official PDB (Protein Data Bank) identifier conventions and standards.

## Official PDB Conventions Followed

### 1. Entry Level Identifiers

#### Experimental Structures
- ✅ **4-character alphanumeric PDB IDs**: All PDB identifiers follow the standard format (e.g., `1il6`, `2h24`, `1f45`)
- ✅ **Case handling**: PDB IDs are automatically converted to uppercase for consistency
- ✅ **Validation**: Invalid PDB ID formats trigger warnings

```r
# Valid PDB IDs
"1il6"  # ✓ 4 characters, alphanumeric
"2h24"  # ✓ 4 characters, alphanumeric
"1F45"  # ✓ Converted to uppercase

# Invalid PDB IDs (trigger warnings)
"il6"    # ✗ Only 3 characters
"12345"  # ✗ 5 characters
"1il6a"  # ✗ 5 characters
```

### 2. Instance Level Identifiers

#### Chain IDs
- ✅ **Flexible chain naming**: Supports both single-character (`A`, `B`) and multi-character (`A1`, `AA`) chain IDs
- ✅ **Author vs PDB assigned**: Handles both `auth_asym_id` and `label_asym_id` conventions
- ✅ **Validation**: Chain IDs are validated against actual PDB file content

```r
# Valid chain specifications
"1f45:A"   # ✓ Single character chain
"1f45:B"   # ✓ Single character chain
"2or1:L"   # ✓ Author-assigned chain (Light chain)
"2or1:R"   # ✓ Author-assigned chain (Heavy chain)
"1abc:A1"  # ✓ Multi-character chain
"1def:AA"  # ✓ Multi-character chain
```

## Implementation Details

### 1. PDB ID Parsing and Validation

```r
parse_pdb_chain <- function(pdb_string) {
  # Validates 4-character alphanumeric format
  if (!grepl("^[0-9A-Za-z]{4}$", pdb_id)) {
    warning("PDB ID does not follow standard 4-character format")
  }
  
  # Converts to uppercase for consistency
  return(list(pdb_id = toupper(pdb_id), chain = chain_id))
}
```

### 2. Chain Validation Against Actual PDB Content

```r
validate_chain_specification <- function(pdb_file, specified_chain) {
  # Reads actual PDB file to verify chain exists
  chain_info <- get_pdb_chain_info(pdb_file)
  
  if (specified_chain %in% chain_info$chains) {
    return(list(valid = TRUE, message = "Chain found"))
  } else {
    return(list(valid = FALSE, message = "Chain not found"))
  }
}
```

### 3. Comprehensive Chain Information

```r
get_pdb_chain_info <- function(pdb_file) {
  # Extracts detailed information about each chain:
  # - Chain ID
  # - Number of atoms (ATOM vs HETATM)
  # - Residue count
  # - Residue types
}
```

## Cytokine-Specific Applications

### IL-12 Family Complexity

The IL-12 family demonstrates why PDB convention compliance is crucial:

```r
# PDB 1F45 contains both IL-12 subunits
"IL12A" = c("1f45:A")  # p35 subunit in chain A
"IL12B" = c("1f45:B")  # p40 subunit in chain B

# PDB 2H24 contains both IL-27 subunits  
"IL27A" = c("2h24:A")  # p28 subunit in chain A
"EBI3" = c("2h24:B")   # EBI3 subunit in chain B
```

### Chain Assignment Logic

Our assignments follow biological relevance:

1. **IL-12** (heterodimer): IL-12A (p35) + IL-12B (p40)
2. **IL-23** (heterodimer): IL-23A (p19) + IL-12B (p40) [shared subunit]
3. **IL-27** (heterodimer): IL-27A (p28) + EBI3
4. **IL-35** (heterodimer): IL-12A (p35) + EBI3 [shared subunits]

## Validation Features

### 1. Format Validation
- PDB IDs must be exactly 4 alphanumeric characters
- Chain IDs must be 1-4 alphanumeric characters
- Warnings issued for non-compliant formats

### 2. Content Validation
- Downloaded PDB files are parsed to verify chain existence
- Chain specifications validated against actual structure content
- Detailed chain information extracted (atom counts, residue types)

### 3. Error Handling
- Graceful handling of missing chains
- Clear error messages for invalid specifications
- Fallback to all chains when specified chain not found

## Benefits of PDB Convention Compliance

### 1. Interoperability
- Compatible with other PDB-based tools and databases
- Consistent with literature references
- Enables cross-referencing with other resources

### 2. Accuracy
- Ensures correct protein sequences are extracted
- Prevents analysis of wrong chains or entities
- Validates biological relevance of chain assignments

### 3. Reproducibility
- Standard identifiers enable exact replication
- Clear documentation of which structures/chains were used
- Consistent naming across different analyses

### 4. Future-Proofing
- Ready for extended PDB ID formats when needed
- Handles both current and legacy naming conventions
- Scalable to new cytokine discoveries

## Testing and Verification

### Automated Tests
Run `test_pdb_conventions.R` to verify:
- PDB ID format validation
- Chain ID format validation
- Known structure compliance
- Actual PDB file chain validation
- Download with chain validation

### Manual Verification
```r
# Check known structures
known_pdbs <- get_known_cytokine_pdbs()

# Validate each entry
for (gene in names(known_pdbs)) {
  for (entry in known_pdbs[[gene]]) {
    parsed <- parse_pdb_chain(entry)
    # Verify PDB ID and chain formats
  }
}
```

## Compliance Checklist

- ✅ 4-character alphanumeric PDB IDs
- ✅ Uppercase PDB ID normalization
- ✅ Flexible chain ID support (1-4 characters)
- ✅ Chain validation against actual PDB content
- ✅ Proper error handling and warnings
- ✅ Detailed chain information extraction
- ✅ Biological relevance in chain assignments
- ✅ Comprehensive testing suite
- ✅ Clear documentation and examples
- ✅ Interoperability with bio3d package

## Future Considerations

### Extended PDB IDs
When the 4-character namespace is exhausted, PDB will use extended formats like `PDB_00001ABC`. Our validation can be easily updated:

```r
# Future-ready validation
if (!grepl("^([0-9A-Za-z]{4}|PDB_[0-9A-Za-z]{8})$", pdb_id)) {
  warning("Invalid PDB ID format")
}
```

### Computed Structure Models (CSMs)
For AlphaFold and other predicted structures:

```r
# Example CSM identifiers
"AF-A0A452S449-F1"  # AlphaFold DB
"ma-bak-cepc-0001"  # ModelArchive
```

Our framework can be extended to handle these namespaced identifiers while maintaining the same chain specification logic.

## Summary

Our cytokine analysis code fully complies with official PDB conventions, ensuring:
- **Accuracy**: Correct identification and processing of protein structures
- **Reproducibility**: Standard identifiers enable exact replication
- **Interoperability**: Compatible with other PDB-based tools
- **Future-proofing**: Ready for evolving PDB standards

This compliance is essential for reliable bioinformatics analysis and scientific reproducibility.
