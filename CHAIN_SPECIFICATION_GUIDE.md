# Chain Specification Guide for PDB Structures

This guide explains how to specify specific chains when downloading and processing PDB structures for cytokine analysis.

## Overview

Many PDB structures contain multiple protein chains (entities). For cytokine analysis, you often want to work with specific chains that represent individual cytokine subunits. The updated functions now support chain specification using a simple format.

## Chain Specification Format

Use the format `"pdb_id:chain"` to specify a particular chain:

- `"1f45:A"` - PDB 1F45, chain A only
- `"1f45:B"` - PDB 1F45, chain B only
- `"1f45"` - PDB 1F45, all chains

## Current Chain Specifications

### IL-6-type Family (Manually Curated)
```r
"IL6" = c("1p9m:B")                       # Interleukin-6
"IL11" = c("8dpt:B", "8dpt:E")           # Interleukin-11
"LIF" = c("8d6a:A")                       # Leukemia inhibitory factor
"OSM" = c("8v29:A")                       # Oncostatin M
"CNTF" = c("8d74:B")                      # Ciliary neurotrophic factor
"CLCF1" = c("8d7h:B", "8d7h:E")          # Cardiotrophin-like cytokine
```

### IL-12 Family (Heterodimeric Cytokines - Manually Curated)
```r
"IL12A" = c("8xrp:A", "8xrp:E", "8xrp:I", "8xrp:M")  # p35 subunit
"IL12B" = c("8xrp:B", "8xrp:F", "8xrp:J", "8xrp:N")  # p40 subunit
"IL23A" = c("6wdq:B")                     # p19 subunit
"IL27A" = c("7u7n:D")                     # p28 subunit
"EBI3" = c("7u7n:C")                      # EBI3 subunit
```

## Why Chain Specification Matters

### 1. IL-12 Family Complexity
IL-12 family cytokines are heterodimers composed of two subunits:
- **IL-12**: IL-12A (p35) + IL-12B (p40)
- **IL-23**: IL-23A (p19) + IL-12B (p40)
- **IL-27**: IL-27A (p28) + EBI3
- **IL-35**: IL-12A (p35) + EBI3

### 2. Shared PDB Structures
Some PDB structures contain multiple subunits:
- **PDB 8XRP**: Contains both IL-12A and IL-12B in multiple chains (A,E,I,M for IL-12A; B,F,J,N for IL-12B)
- **PDB 7U7N**: Contains both IL-27A (chain D) and EBI3 (chain C)
- **PDB 8DPT**: Contains IL-11 in multiple chains (B and E)

### 3. Accurate Analysis
Chain specification ensures:
- Correct sequence extraction for each subunit
- Proper structure-based alignments
- Accurate phylogenetic analysis
- Correct functional domain identification

## Function Usage

### 1. Download with Chain Specification
```r
# Download IL-12A (p35 subunit, multiple chains from 8XRP)
result <- download_pdb_structures("IL12A", "structures")

# This downloads PDB 8XRP and records that we want chains A,E,I,M
```

### 2. Automatic Chain Processing
```r
# Trimming automatically uses the specified chain
trimmed_file <- trim_pdb_structure(pdb_file, chain = "A")

# Output filename includes chain: "8xrp_chainA_trimmed.pdb"
```

### 3. Batch Processing
```r
# Process multiple genes with their specified chains
genes <- c("IL12A", "IL12B", "IL27A", "EBI3")
results <- batch_process_structures(genes, "structures")

# Each gene is processed with its appropriate chain
```

## Example: IL-12 vs IL-23 Analysis

Both IL-12 and IL-23 share the IL-12B (p40) subunit but have different alpha subunits:

```r
# IL-12 components
il12a_result <- download_pdb_structures("IL12A")  # Gets 8xrp:A,E,I,M
il12b_result <- download_pdb_structures("IL12B")  # Gets 8xrp:B,F,J,N

# IL-23 components
il23a_result <- download_pdb_structures("IL23A")  # Gets 6wdq:B
# IL12B is shared with IL-12

# This allows proper comparison of:
# - IL-12A vs IL-23A (different alpha subunits)
# - IL-12B sequences from different structures
```

## Chain Information Tracking

The functions track chain information throughout the pipeline:

```r
result <- download_pdb_structures("IL12A")

# Chain information is stored
result$chain_info
# $`structures/1f45.pdb`
# $pdb_id: "1f45"
# $chain: "A"
# $entry: "1f45:A"
```

## Verification

### Check Available Chains
```r
pdb <- read.pdb("1f45.pdb")
unique(pdb$atom$chain)  # Shows: "A" "B"
```

### Verify Chain Selection
```r
# Before trimming: shows all chains
# After trimming with chain="A": shows only chain A
```

## Adding New Structures

To add new cytokine structures with chain specification:

```r
# In get_known_cytokine_pdbs() function:
"NEW_CYTOKINE" = c("1abc:A", "2def:B", "3ghi")  # Mix of specific and all chains
```

## Best Practices

1. **Always specify chains** for multi-chain structures
2. **Use biological relevance** - specify the chain that contains your protein of interest
3. **Document chain assignments** - add comments explaining which subunit each chain represents
4. **Verify chain contents** - check that the specified chain contains the expected protein
5. **Consider all isoforms** - some cytokines may have multiple chain arrangements

## Troubleshooting

### Chain Not Found
```
Warning: Chain X not found. Available chains: A, B
Using all chains...
```
**Solution**: Check the PDB structure and update the chain specification.

### Empty Structure After Chain Selection
**Cause**: Specified chain doesn't contain protein atoms (might be DNA, RNA, or ligands)
**Solution**: Examine the PDB structure and choose the correct protein chain.

### Multiple Copies of Same Chain
**Cause**: Some structures have multiple copies of the same protein
**Solution**: Usually chain A is sufficient, but check biological assembly if needed.

## Impact on Analysis

Chain specification improves:

1. **Sequence Alignments**: Only relevant protein sequences are aligned
2. **Structure Alignments**: Homologous domains are properly compared
3. **Conservation Analysis**: Functional domains are correctly identified
4. **Phylogenetic Trees**: Evolutionary relationships are more accurate
5. **Functional Annotation**: Domain boundaries and active sites are preserved

This feature is essential for accurate analysis of multi-subunit cytokines like the IL-12 family.
