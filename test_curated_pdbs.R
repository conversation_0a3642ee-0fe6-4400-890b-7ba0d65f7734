# Test Script for Manually Curated PDB Accessions
# Verifies that all manually curated PDB structures work correctly

# Load required libraries
library(bio3d)
library(httr)

cat("=== Testing Manually Curated PDB Accessions ===\n\n")

# Load the manually curated PDB list
get_known_cytokine_pdbs <- function() {
  known_structures <- list(
    # IL-6-type family cytokines
    "IL6" = c("1p9m:B"),                       # Interleukin-6
    "IL11" = c("8dpt:B", "8dpt:E"),           # Interleukin-11
    "LIF" = c("8d6a:A"),                       # Leukemia inhibitory factor
    "OSM" = c("8v29:A"),                       # Oncostatin M
    "CNTF" = c("8d74:B"),                      # Ciliary neurotrophic factor
    "CLCF1" = c("8d7h:B", "8d7h:E"),          # Cardiotrophin-like cytokine

    # IL-12 family cytokine subunits
    "IL12A" = c("8xrp:A", "8xrp:E", "8xrp:I", "8xrp:M"),  # IL-12 p35 subunit
    "IL12B" = c("8xrp:B", "8xrp:F", "8xrp:J", "8xrp:N"),  # IL-12 p40 subunit
    "IL23A" = c("6wdq:B"),                     # IL-23 p19 subunit
    "IL27A" = c("7u7n:D"),                     # IL-27 p28 subunit
    "EBI3" = c("7u7n:C")                       # EBI3 subunit
  )
  
  return(known_structures)
}

# Parse PDB ID and chain
parse_pdb_chain <- function(pdb_string) {
  if (grepl(":", pdb_string)) {
    parts <- strsplit(pdb_string, ":")[[1]]
    return(list(pdb_id = toupper(parts[1]), chain = parts[2]))
  } else {
    return(list(pdb_id = toupper(pdb_string), chain = NULL))
  }
}

# Test 1: Validate all manually curated PDB accessions
cat("1. Validating manually curated PDB accessions:\n")

known_pdbs <- get_known_cytokine_pdbs()
all_pdb_ids <- character()
all_entries <- character()

for (gene in names(known_pdbs)) {
  cat("  ", gene, ":\n")
  
  for (entry in known_pdbs[[gene]]) {
    parsed <- parse_pdb_chain(entry)
    pdb_id <- parsed$pdb_id
    chain <- parsed$chain
    
    all_pdb_ids <- c(all_pdb_ids, pdb_id)
    all_entries <- c(all_entries, entry)
    
    # Validate PDB ID format
    valid_format <- grepl("^[0-9A-Za-z]{4}$", pdb_id)
    
    cat("    ", entry, "-> PDB:", pdb_id, ", Chain:", 
        if(is.null(chain)) "All" else chain)
    
    if (valid_format) {
      cat(" ✓\n")
    } else {
      cat(" ✗ Invalid format\n")
    }
  }
}

# Get unique PDB IDs
unique_pdb_ids <- unique(all_pdb_ids)
cat("\nUnique PDB IDs to test:", length(unique_pdb_ids), "\n")
cat("PDB IDs:", paste(unique_pdb_ids, collapse = ", "), "\n")

# Test 2: Test downloading a few representative structures
cat("\n2. Testing PDB downloads for representative structures:\n")

test_pdbs <- c("1p9m", "8dpt", "8xrp", "7u7n")  # Representative from each family
test_dir <- "test_curated_pdbs"
dir.create(test_dir, showWarnings = FALSE)

download_results <- list()

for (pdb_id in test_pdbs) {
  cat("  Testing", pdb_id, "...")
  
  tryCatch({
    pdb_file <- get.pdb(pdb_id, path = test_dir)
    
    if (file.exists(pdb_file)) {
      download_results[[pdb_id]] <- pdb_file
      cat(" ✓ Downloaded\n")
    } else {
      cat(" ✗ Download failed\n")
    }
    
  }, error = function(e) {
    cat(" ✗ Error:", e$message, "\n")
  })
  
  Sys.sleep(1)  # Be respectful to PDB servers
}

# Test 3: Validate chain specifications
cat("\n3. Testing chain specifications:\n")

test_chains <- list(
  "1p9m" = "B",    # IL6
  "8dpt" = c("B", "E"),  # IL11
  "8xrp" = c("A", "B"),  # IL12A/IL12B
  "7u7n" = c("C", "D")   # EBI3/IL27A
)

for (pdb_id in names(test_chains)) {
  if (pdb_id %in% names(download_results)) {
    pdb_file <- download_results[[pdb_id]]
    
    cat("  ", pdb_id, ":\n")
    
    tryCatch({
      pdb <- read.pdb(pdb_file)
      available_chains <- unique(pdb$atom$chain)
      
      cat("    Available chains:", paste(available_chains, collapse = ", "), "\n")
      
      for (chain in test_chains[[pdb_id]]) {
        if (chain %in% available_chains) {
          cat("    Chain", chain, ": ✓ Found\n")
        } else {
          cat("    Chain", chain, ": ✗ Not found\n")
        }
      }
      
    }, error = function(e) {
      cat("    Error reading PDB:", e$message, "\n")
    })
  }
}

# Test 4: Test protein sequence retrieval for comparison
cat("\n4. Testing protein sequence retrieval for validation:\n")

get_protein_sequence <- function(gene_name, organism_id = 9606) {
  url <- paste0("https://rest.uniprot.org/uniprotkb/search?query=(gene_exact:", 
                gene_name, ")+AND+(organism_id:", organism_id, ")+AND+(reviewed:true)&format=fasta&limit=1")
  
  response <- GET(url)
  
  if (status_code(response) == 200) {
    content_raw <- content(response, "raw")
    
    tryCatch({
      content_text <- memDecompress(content_raw, type = "gzip", asChar = TRUE)
      return(content_text)
    }, error = function(e) {
      tryCatch({
        content_text <- rawToChar(content_raw)
        return(content_text)
      }, error = function(e2) {
        return(NULL)
      })
    })
  }
  
  return(NULL)
}

# Test a few genes
test_genes <- c("IL6", "IL11", "IL12A")

for (gene in test_genes) {
  cat("  ", gene, "...")
  
  result <- get_protein_sequence(gene)
  
  if (!is.null(result)) {
    lines <- strsplit(result, "\n")[[1]]
    sequence <- paste(lines[-1], collapse = "")
    cat(" ✓ Sequence length:", nchar(sequence), "aa\n")
  } else {
    cat(" ✗ Failed\n")
  }
  
  Sys.sleep(1)
}

# Test 5: Summary and validation
cat("\n5. Summary of manually curated PDB validation:\n")

cat("Total cytokine genes:", length(known_pdbs), "\n")
cat("Total PDB entries:", length(all_entries), "\n")
cat("Unique PDB structures:", length(unique_pdb_ids), "\n")
cat("Successfully downloaded:", length(download_results), "out of", length(test_pdbs), "tested\n")

# Check for any issues
issues <- character()

# Check for duplicate PDB:chain combinations
entry_counts <- table(all_entries)
duplicates <- names(entry_counts)[entry_counts > 1]
if (length(duplicates) > 0) {
  issues <- c(issues, paste("Duplicate entries:", paste(duplicates, collapse = ", ")))
}

# Check for invalid PDB ID formats
invalid_pdbs <- unique_pdb_ids[!grepl("^[0-9A-Za-z]{4}$", unique_pdb_ids)]
if (length(invalid_pdbs) > 0) {
  issues <- c(issues, paste("Invalid PDB formats:", paste(invalid_pdbs, collapse = ", ")))
}

if (length(issues) == 0) {
  cat("✓ All manually curated PDB accessions are valid!\n")
} else {
  cat("✗ Issues found:\n")
  for (issue in issues) {
    cat("  -", issue, "\n")
  }
}

# Cleanup
cat("\nTest files created in:", test_dir, "\n")
cat("You can delete the test directory if not needed:\n")
cat("  unlink('", test_dir, "', recursive = TRUE)\n", sep = "")

cat("\n=== Manually Curated PDB Test Complete ===\n")
