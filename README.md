# IL-6-type and IL-12 Family Cytokine Sequence Analysis

This R Mark<PERSON> script performs comprehensive sequence acquisition and alignment analysis for IL-6-type and IL-12 family cytokines, following the methodology established for chemokine analysis.

## Overview

The analysis includes:

1. **Sequence Acquisition**: Downloads canonical sequences from UniProt for human cytokines
2. **Structure Acquisition**: Searches and downloads experimental structures from PDB
3. **Structure Processing**: Trims PDB structures using Bio3D package
4. **Structure-based Alignment**: Uses MUSTANG for structure-based sequence alignment
5. **Sequence-based Alignment**: Uses MUSCLE for sequence-only alignment
6. **Alignment Integration**: Combines structure and sequence alignments
7. **Ortholog Analysis**: Acquires and aligns orthologous sequences
8. **Visualization**: Creates conservation plots and family comparisons

## Cytokine Families Analyzed

### IL-6-type Family (8 members)
All share the gp130 receptor subunit:
- IL-6 (Interleukin-6)
- IL-11 (Interleukin-11)
- CNTF (Ciliary neurotrophic factor)
- LIF (Leukemia inhibitory factor)
- OSM (Oncostatin M)
- CT-1 (Cardiotrophin-1)
- CLC (Cardiotrophin-like cytokine)
- IL-27 (Interleukin-27)

### IL-12 Family (5 subunits forming 4 heterodimers)
Subunits:
- IL-12A (p35 subunit)
- IL-12B (p40 subunit)
- IL-23A (p19 subunit)
- IL-27A (p28 subunit)
- EBI3 (Epstein-Barr virus induced 3)

Heterodimeric cytokines formed:
- IL-12 (IL-12A + IL-12B)
- IL-23 (IL-23A + IL-12B)
- IL-27 (IL-27A + EBI3)
- IL-35 (IL-12A + EBI3)

## Prerequisites

### Required R Packages

```r
# Bioconductor packages
if (!require("BiocManager", quietly = TRUE))
    install.packages("BiocManager")

BiocManager::install(c("bio3d", "Biostrings", "muscle"))

# CRAN packages
install.packages(c("seqinr", "httr", "xml2", "dplyr", "ggplot2", 
                   "knitr", "DT", "tidyr"))
```

### External Software (Optional but Recommended)

1. **MUSCLE**: For high-quality sequence alignment
   - Download from: https://www.drive5.com/muscle/
   - Add to system PATH

2. **MUSTANG**: For structure-based alignment
   - Included in Bio3D package
   - Requires Bio3D installation

## Usage

1. **Open the R Markdown file**: `cytokine_sequence_analysis.Rmd`

2. **Install required packages** (see Prerequisites above)

3. **Run the analysis**:
   - In RStudio: Click "Knit" button
   - In R console: `rmarkdown::render("cytokine_sequence_analysis.Rmd")`

4. **Output files will be created in**:
   - `sequences/`: FASTA files for individual and grouped sequences
   - `structures/`: Downloaded and processed PDB structures
   - `alignments/`: Multiple sequence alignments
   - `results/`: Analysis results and plots

## Key Features

### Automated Data Acquisition
- Queries UniProt REST API for canonical sequences
- Searches PDB for experimental structures
- Downloads and processes structural data

### Multiple Alignment Strategies
- Structure-based alignment using MUSTANG
- Sequence-based alignment using MUSCLE
- Integration of both approaches

### Comprehensive Analysis
- Sequence composition analysis
- Conservation scoring
- Family-wise comparisons
- Visualization of results

### Ortholog Integration
- Framework for ortholog acquisition (OMA/Ensembl)
- Ortholog-specific alignments
- Cross-species conservation analysis

## Methodology

This script follows the same rigorous methodology used for chemokine analysis:

1. **Sequence Collection**: Canonical sequences from UniProt
2. **Structure Processing**: PDB structures trimmed with Bio3D
3. **MUSTANG Alignment**: Structure-based alignment for proteins with known structures
4. **MUSCLE Alignment**: Independent sequence-based alignment
5. **Integration**: Bridging structure and sequence alignments
6. **Manual Refinement**: Framework for manual inspection (using Jalview)
7. **Ortholog Analysis**: Cross-species sequence acquisition and alignment

## Output

The script generates:

- **HTML Report**: Comprehensive analysis with plots and tables
- **Sequence Files**: FASTA format files for all sequences
- **Alignment Files**: Multiple sequence alignments in FASTA format
- **Structure Files**: Processed PDB structures
- **Plots**: Conservation profiles and family comparisons

## Customization

### Adding New Cytokines
Modify the cytokine lists in the script:

```r
# Add new IL-6-type family members
il6_family <- c("IL6", "IL11", "CNTF", "LIF", "OSM", "CTF1", "CLCF1", "IL27", "NEW_GENE")

# Add new IL-12 family members
il12_family <- c("IL12A", "IL12B", "IL23A", "IL27A", "EBI3", "NEW_SUBUNIT")
```

### Modifying Analysis Parameters
- Adjust conservation scoring methods
- Change visualization parameters
- Modify alignment parameters

## Troubleshooting

### Common Issues

1. **Network connectivity**: Ensure internet access for UniProt/PDB queries
2. **Package installation**: Install Bioconductor packages separately if needed
3. **MUSCLE not found**: Install MUSCLE separately or use built-in alignment
4. **Memory issues**: Process families separately for large datasets

### Performance Tips

- Run analysis in chunks for large datasets
- Use local copies of databases when available
- Adjust timeout parameters for slow connections

## References

This methodology is based on established protocols for:
- Chemokine sequence analysis and alignment
- Structure-based sequence alignment (MUSTANG)
- Multiple sequence alignment (MUSCLE)
- Phylogenetic analysis of cytokine families

## License

This script is provided for research purposes. Please cite appropriately when used in publications.
