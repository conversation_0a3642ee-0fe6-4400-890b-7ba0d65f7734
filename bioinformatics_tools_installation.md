# MUSTANG and MUSCLE Installation Guide for macOS

## Overview
This guide will help you install <PERSON><PERSON><PERSON><PERSON> (structure alignment) and MUSCL<PERSON> (sequence alignment) tools for your cytokine analysis pipeline.

## Installation Strategy

### Option 1: Homebrew + Bioconda (Recommended)
This approach provides the best package management and integration.

### Option 2: Direct Download and Manual Installation
Fallback option if package managers don't work.

---

## Step 1: Install Conda/Miniconda (Recommended)

First, let's install Miniconda for better bioinformatics package management:

```bash
# Download Miniconda for macOS
curl -O https://repo.anaconda.com/miniconda/Miniconda3-latest-MacOSX-x86_64.sh

# Install Miniconda
bash Miniconda3-latest-MacOSX-x86_64.sh

# Follow the prompts:
# - Accept license terms
# - Install to default location (/Users/<USER>/miniconda3)
# - Allow conda to initialize your shell

# Restart your terminal or run:
source ~/.bash_profile
```

## Step 2: Set Up Bioconda Channel

```bash
# Add bioconda channels (in correct order)
conda config --add channels defaults
conda config --add channels bioconda
conda config --add channels conda-forge

# Set channel priority
conda config --set channel_priority strict
```

## Step 3: Install MUSCLE

### Option A: Via Conda (Recommended)
```bash
# Install MUSCLE
conda install muscle

# Verify installation
muscle -version
which muscle
```

### Option B: Via Homebrew
```bash
# Install MUSCLE via Homebrew
brew install muscle

# Verify installation
muscle -version
```

### Option C: Manual Installation
```bash
# Create tools directory
mkdir -p ~/bioinformatics-tools/muscle
cd ~/bioinformatics-tools/muscle

# Download MUSCLE for macOS
curl -O https://github.com/rcedgar/muscle/releases/download/v5.1/muscle5.1.macos_intel64
chmod +x muscle5.1.macos_intel64

# Create symlink for easier access
ln -s muscle5.1.macos_intel64 muscle

# Add to PATH (add this line to ~/.bash_profile or ~/.zshrc)
export PATH="$HOME/bioinformatics-tools/muscle:$PATH"
```

## Step 4: Install MUSTANG

MUSTANG is more complex as it's not in standard package repositories.

### Option A: Manual Installation (Required)
```bash
# Create MUSTANG directory
mkdir -p ~/bioinformatics-tools/mustang
cd ~/bioinformatics-tools/mustang

# Download MUSTANG source code
curl -O http://lcb.infotech.monash.edu.au/mustang/mustang_v3.2.4.tgz

# Extract
tar -xzf mustang_v3.2.4.tgz
cd mustang_v3.2.4

# Compile (requires Xcode command line tools)
make

# Copy binary to tools directory
cp bin/mustang ~/bioinformatics-tools/mustang/

# Add to PATH (add this line to ~/.bash_profile or ~/.zshrc)
export PATH="$HOME/bioinformatics-tools/mustang:$PATH"
```

### Alternative: Try Homebrew Science (if available)
```bash
# This may or may not work depending on available taps
brew search mustang
```

## Step 5: Update Your Shell Configuration

Add the following lines to your `~/.bash_profile` (or `~/.zshrc` if using zsh):

```bash
# Bioinformatics tools
export PATH="$HOME/bioinformatics-tools/muscle:$PATH"
export PATH="$HOME/bioinformatics-tools/mustang:$PATH"

# If using conda
export PATH="$HOME/miniconda3/bin:$PATH"
```

## Step 6: Verify Installation

```bash
# Restart terminal or reload profile
source ~/.bash_profile

# Test MUSCLE
muscle -version

# Test MUSTANG
mustang -h

# Check paths
which muscle
which mustang
```

## Directory Structure

Your bioinformatics tools will be organized as:

```
~/bioinformatics-tools/
├── muscle/
│   ├── muscle5.1.macos_intel64
│   └── muscle -> muscle5.1.macos_intel64
└── mustang/
    ├── mustang_v3.2.4.tgz
    ├── mustang_v3.2.4/
    └── mustang
```

## Integration with R

Once installed, your R scripts will automatically find these tools since they're in your PATH.

## Troubleshooting

### If MUSTANG compilation fails:
1. Install Xcode command line tools: `xcode-select --install`
2. Check for missing dependencies
3. Try alternative download sources

### If MUSCLE doesn't work:
1. Check file permissions: `chmod +x muscle`
2. Verify architecture compatibility
3. Try different version

### Path issues:
1. Verify PATH with: `echo $PATH`
2. Check shell configuration file
3. Restart terminal

## Performance Expectations

Based on testing with your cytokine structures:

### MUSCLE Runtime:
- **Sequence alignment**: Very fast (seconds)
- **2 sequences (IL6, IL11)**: < 1 second
- **Multiple sequences**: Still very fast (< 30 seconds for most cases)

### MUSTANG Runtime:
- **2 structures (662 + 963 residues)**: ~4 minutes (230 seconds)
- **6 IL-6 family structures**: 15-30+ minutes (estimated)
- **Larger/more diverse structures**: Can take 30-60+ minutes

### Runtime Factors:
1. **Structure size** (number of residues)
2. **Number of structures** (complexity increases exponentially)
3. **Structural diversity** (more different = longer alignment)
4. **System resources** (CPU, memory)

## Installation Summary

✅ **Successfully Installed:**
- **MUSCLE v5.2** via `brewsci/bio/muscle`
- **MUSTANG v3.2.4** via `brewsci/bio/mustang`
- Both tools are in `/usr/local/bin/` and accessible from R

## Next Steps

After installation, run the test script again to verify everything works with your R environment.

## Optimization Tips

For large datasets:
1. **Start with fewer structures** to test workflows
2. **Use subsets** for initial analysis
3. **Run overnight** for large MUSTANG alignments
4. **Consider parallel processing** for multiple families
