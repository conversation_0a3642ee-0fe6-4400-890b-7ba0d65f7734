# Test script for structure_processing chunk
library(bio3d)

# Create test directories
dir.create("structures", showWarnings = FALSE)
dir.create("alignments", showWarnings = FALSE)

# Function to trim PDB structures (remove waters, cofactors, etc.)
trim_pdb_structure <- function(pdb_file, output_file = NULL) {
  if (!file.exists(pdb_file)) {
    cat("PDB file not found:", pdb_file, "\n")
    return(NULL)
  }

  tryCatch({
    # Read PDB file
    pdb <- read.pdb(pdb_file)
    cat("Original PDB:", basename(pdb_file), "- atoms:", nrow(pdb$atom), "\n")

    # Trim to protein only (removes waters, ligands, etc.)
    pdb_trimmed <- trim.pdb(pdb, "protein")
    cat("Trimmed PDB: atoms:", nrow(pdb_trimmed$atom), "\n")

    # Generate output filename if not provided
    if (is.null(output_file)) {
      output_file <- gsub("\\.pdb$", "_trimmed.pdb", pdb_file)
    }

    # Save trimmed structure
    write.pdb(pdb_trimmed, file = output_file)

    if (file.exists(output_file)) {
      cat("✓ Trimmed structure saved:", basename(output_file), "\n")
      return(output_file)
    } else {
      cat("✗ Failed to save trimmed structure\n")
      return(NULL)
    }

  }, error = function(e) {
    cat("Error trimming PDB:", e$message, "\n")
    return(NULL)
  })
}

# Test the function with mock data
cat("Testing structure_processing chunk...\n")

# Check if we have any PDB files to test with
pdb_files <- list.files("structures", pattern = "\\.pdb$", full.names = TRUE)

if (length(pdb_files) == 0) {
  cat("No PDB files found in structures/ directory.\n")
  cat("Let's download a test PDB file first...\n")

  # Download a test PDB file (IL-6 structure)
  tryCatch({
    test_pdb <- get.pdb("1p9m", path = "structures")
    cat("Downloaded test PDB file:", test_pdb, "\n")
    pdb_files <- c(test_pdb)
  }, error = function(e) {
    cat("Error downloading test PDB:", e$message, "\n")
    cat("Cannot test structure processing without PDB files.\n")
    quit(status = 1)
  })
}

# Test trimming function on available PDB files
trimmed_structures <- list()
test_gene <- "TEST"

cat("Processing structures for", test_gene, "...\n")

gene_trimmed <- character()

for (pdb_file in pdb_files[1:min(2, length(pdb_files))]) {  # Test max 2 files
  if (file.exists(pdb_file)) {
    cat("Testing with file:", basename(pdb_file), "\n")
    trimmed_file <- trim_pdb_structure(pdb_file)

    if (!is.null(trimmed_file)) {
      gene_trimmed <- c(gene_trimmed, trimmed_file)
    }
  }
}

if (length(gene_trimmed) > 0) {
  trimmed_structures[[test_gene]] <- gene_trimmed
  cat("  ✓ Processed", length(gene_trimmed), "structures for", test_gene, "\n")
} else {
  cat("  ✗ No structures were successfully processed\n")
}

cat("Total trimmed structures:", sum(sapply(trimmed_structures, length)), "\n")

# Test completed
cat("\nStructure processing test completed!\n")
cat("Results:\n")
cat("- Input PDB files:", length(pdb_files), "\n")
cat("- Successfully trimmed:", length(gene_trimmed), "\n")

if (length(gene_trimmed) > 0) {
  cat("- Output files:\n")
  for (file in gene_trimmed) {
    cat("  ", basename(file), "\n")
  }
}
