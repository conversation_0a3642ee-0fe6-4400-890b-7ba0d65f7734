# Quick test of MUSTANG and MUSCLE with smaller dataset
library(bio3d)

# Create test directories
dir.create("alignments", showWarnings = FALSE)

cat("=== Quick Test of MUSTANG and MUSCLE ===\n")

# Test 1: MUSCLE with a simple sequence alignment
cat("\n1. Testing MUSCLE with sequences...\n")

# Create a simple test FASTA file
test_sequences <- c(
  ">IL6_human",
  "MNSFSTSAFGPVAFSLGLLLVLPAAFPAPVPPGEDSKDVAAPHRQPLTSSERIDKQIRYILDGISALRKETCNKSNMCESSKEALAENNLNLPKMAEKDGCFQSGFNEETCLVKIITGLLEFEVYLEYLQNRFESSEEQARAVQMSTKVLIQFLQKKAKNLDAITTPDPTTNASLLTKLQAQNQWLQDMTTHLILRSPHQYSQVLFKGQGCPSTHVLLTHTISRIAVSYQTKVNLLSAIKSPCQRETPEGAEAKPWYEPIYLGGVFQLEKGDRLSAEINRPDYLDFAESGQVYFGIIAL",
  ">IL11_human", 
  "MNCVCTGKRWDPCLQVLGLLLLSLSLLSLWKQGAAPRGPPPGPPRVSPDPRAELDSTVLLTRSLLADTRQLAAQLFADTLKSLQETDKGFQGQGSLCMFRSFQSASTEKGFLLRIQCLAHRLNQSSQVPWEAPLQLHVDKAVSGLRSLTTLLRALGAQKEAISPPDAASAAPLRTITADTFRKLFRVYSNFLRGKLKLYTGEACRTGDR"
)

writeLines(test_sequences, "test_sequences.fasta")

# Test MUSCLE
muscle_result <- system("muscle -align test_sequences.fasta -output test_muscle_output.fasta", intern = TRUE)
if (file.exists("test_muscle_output.fasta")) {
  cat("✓ MUSCLE test successful!\n")
  lines <- readLines("test_muscle_output.fasta")
  cat("  Output file contains", length(lines), "lines\n")
} else {
  cat("✗ MUSCLE test failed\n")
}

# Test 2: MUSTANG with 2 structures (much faster)
cat("\n2. Testing MUSTANG with 2 structures...\n")

# Get two small structures for quick test
test_structures <- c("structures/1p9m_trimmed.pdb", "structures/8D6A_trimmed.pdb")
available_structures <- test_structures[file.exists(test_structures)]

if (length(available_structures) >= 2) {
  cat("Testing MUSTANG with:", basename(available_structures[1:2]), "\n")
  
  # Use bio3d's mustang function for easier testing
  tryCatch({
    mustang_result <- mustang(available_structures[1:2], 
                             outfile = "alignments/quick_test_mustang.fasta")
    
    if (file.exists("alignments/quick_test_mustang.fasta")) {
      cat("✓ MUSTANG test successful!\n")
      lines <- readLines("alignments/quick_test_mustang.fasta")
      cat("  Output file contains", length(lines), "lines\n")
    } else {
      cat("✗ MUSTANG output file not found\n")
    }
    
  }, error = function(e) {
    cat("✗ MUSTANG test failed:", e$message, "\n")
  })
  
} else {
  cat("Not enough structures available for MUSTANG test\n")
}

# Test 3: Check tool versions and paths
cat("\n3. Tool verification...\n")
cat("MUSCLE version:\n")
system("muscle -version")
cat("\nMUSCLE location:", system("which muscle", intern = TRUE), "\n")

cat("\nMUSTANG location:", system("which mustang", intern = TRUE), "\n")

# Clean up test files
file.remove("test_sequences.fasta")
if (file.exists("test_muscle_output.fasta")) file.remove("test_muscle_output.fasta")

cat("\n=== Test completed! ===\n")
cat("\nRuntime expectations:\n")
cat("- MUSCLE: Very fast (seconds for most alignments)\n")
cat("- MUSTANG with 2 structures: 30 seconds - 2 minutes\n")
cat("- MUSTANG with 6 structures: 5-15 minutes\n")
cat("- MUSTANG with 6 large structures (like cytokines): 15-30+ minutes\n")
cat("\nBoth tools are now properly installed and working!\n")
