# Working Functions for Protein Sequence Retrieval from Gene Names
# Tested and verified to work with UniProt REST API

# Load required libraries
library(httr)

# Main function to get protein sequence from gene name
get_protein_sequence <- function(gene_name, organism_id = 9606) {
  # Build URL for UniProt REST API
  url <- paste0("https://rest.uniprot.org/uniprotkb/search?query=(gene_exact:", 
                gene_name, ")+AND+(organism_id:", organism_id, ")+AND+(reviewed:true)&format=fasta&limit=1")
  
  # Make HTTP request
  response <- GET(url)
  
  if (status_code(response) == 200) {
    # Get raw content
    content_raw <- content(response, "raw")
    
    # Try to decompress if it's gzipped (UniProt often returns gzipped content)
    tryCatch({
      content_text <- memDecompress(content_raw, type = "gzip", asChar = TRUE)
      return(content_text)
    }, error = function(e) {
      # If decompression fails, try as plain text
      tryCatch({
        content_text <- rawToChar(content_raw)
        return(content_text)
      }, error = function(e2) {
        return(NULL)
      })
    })
  }
  
  return(NULL)
}

# Enhanced function that returns structured information
get_protein_info <- function(gene_name, organism_id = 9606) {
  # Get FASTA sequence
  fasta_content <- get_protein_sequence(gene_name, organism_id)
  
  if (!is.null(fasta_content) && nchar(fasta_content) > 0) {
    # Parse FASTA content
    lines <- strsplit(fasta_content, "\n")[[1]]
    header <- lines[1]
    sequence_lines <- lines[-1]
    sequence <- paste(sequence_lines, collapse = "")
    
    # Extract UniProt ID from header
    uniprot_id <- NA
    if (grepl("\\|", header)) {
      parts <- strsplit(header, "\\|")[[1]]
      if (length(parts) >= 2) {
        uniprot_id <- parts[2]
      }
    }
    
    # Extract protein name
    protein_name <- gsub("^>.*?\\s+", "", header)
    protein_name <- gsub("\\s+OS=.*$", "", protein_name)
    
    return(list(
      gene = gene_name,
      uniprot_id = uniprot_id,
      protein_name = protein_name,
      sequence = sequence,
      length = nchar(sequence),
      fasta = fasta_content,
      header = header
    ))
  }
  
  return(NULL)
}

# Batch function to download multiple sequences
batch_download_sequences <- function(gene_list, output_file = NULL, delay = 0.5) {
  sequences <- list()
  
  cat("Downloading sequences for", length(gene_list), "genes...\n")
  
  for (i in seq_along(gene_list)) {
    gene <- gene_list[i]
    cat(sprintf("[%d/%d] Processing %s...", i, length(gene_list), gene))
    
    info <- get_protein_info(gene)
    
    if (!is.null(info)) {
      sequences[[gene]] <- info
      cat(" ✓ Success (", info$length, " aa)\n")
    } else {
      cat(" ✗ Failed\n")
    }
    
    # Be respectful to the API
    if (i < length(gene_list)) {
      Sys.sleep(delay)
    }
  }
  
  # Save to file if specified
  if (!is.null(output_file) && length(sequences) > 0) {
    fasta_content <- sapply(sequences, function(x) x$fasta)
    writeLines(fasta_content, output_file)
    cat("Saved", length(sequences), "sequences to", output_file, "\n")
  }
  
  return(sequences)
}

# Function to create summary table
create_sequence_summary <- function(sequence_data) {
  if (length(sequence_data) == 0) {
    return(data.frame())
  }
  
  summary_df <- do.call(rbind, lapply(names(sequence_data), function(gene) {
    info <- sequence_data[[gene]]
    data.frame(
      Gene = gene,
      UniProt_ID = info$uniprot_id,
      Protein_Name = info$protein_name,
      Length = info$length,
      stringsAsFactors = FALSE
    )
  }))
  
  return(summary_df)
}

# Example usage:
if (FALSE) {  # Set to TRUE to run examples
  
  # Single gene
  il6_info <- get_protein_info("IL6")
  print(il6_info)
  
  # Multiple genes
  cytokine_genes <- c("IL6", "IL11", "CNTF", "LIF", "OSM", "IL12A", "IL12B")
  cytokine_sequences <- batch_download_sequences(cytokine_genes, "cytokine_sequences.fasta")
  
  # Create summary
  summary_table <- create_sequence_summary(cytokine_sequences)
  print(summary_table)
}

# Alternative function using different approach (if the above doesn't work)
get_protein_sequence_alternative <- function(gene_name, organism_id = 9606) {
  # Try direct UniProt entry lookup
  search_url <- paste0("https://rest.uniprot.org/uniprotkb/search?query=gene:", gene_name, 
                      "+AND+organism_id:", organism_id, "+AND+reviewed:true&format=fasta&limit=1")
  
  response <- GET(search_url)
  
  if (status_code(response) == 200) {
    content_text <- content(response, "text", encoding = "UTF-8")
    
    # Handle potential NA or empty content
    if (!is.null(content_text) && !is.na(content_text) && nchar(content_text) > 0) {
      return(content_text)
    }
  }
  
  return(NULL)
}

cat("Protein sequence retrieval functions loaded successfully!\n")
cat("Main functions:\n")
cat("  - get_protein_sequence(gene_name): Get FASTA sequence\n")
cat("  - get_protein_info(gene_name): Get structured information\n")
cat("  - batch_download_sequences(gene_list): Download multiple sequences\n")
cat("  - create_sequence_summary(sequence_data): Create summary table\n")
