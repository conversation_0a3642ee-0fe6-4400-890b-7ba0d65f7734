# Test Script for Protein Sequence Retrieval Functions
# Run this to verify the functions work before running the full R Markdown

# Load required libraries
library(httr)

# Source the working functions
source("working_protein_sequence_functions.R")

# Test with a few cytokine genes
test_genes <- c("IL6", "IL11", "CNTF", "IL12A")

cat("=== Testing Protein Sequence Retrieval Functions ===\n\n")

# Test individual function
cat("1. Testing individual gene lookup:\n")
il6_info <- get_protein_info("IL6")

if (!is.null(il6_info)) {
  cat("✓ SUCCESS: IL6 found\n")
  cat("  UniProt ID:", il6_info$uniprot_id, "\n")
  cat("  Protein Name:", il6_info$protein_name, "\n")
  cat("  Length:", il6_info$length, "amino acids\n")
  cat("  Sequence preview:", substr(il6_info$sequence, 1, 50), "...\n\n")
} else {
  cat("✗ FAILED: Could not retrieve IL6\n\n")
}

# Test batch function
cat("2. Testing batch download:\n")
batch_results <- batch_download_sequences(test_genes, "test_sequences.fasta", delay = 1)

if (length(batch_results) > 0) {
  cat("\n✓ Batch download successful!\n")
  cat("Retrieved", length(batch_results), "out of", length(test_genes), "sequences\n")
  
  # Create summary
  summary_table <- create_sequence_summary(batch_results)
  print(summary_table)
  
  cat("\nSequences saved to: test_sequences.fasta\n")
} else {
  cat("\n✗ Batch download failed\n")
}

cat("\n=== Test Complete ===\n")

# Clean up test file
if (file.exists("test_sequences.fasta")) {
  cat("Test file created successfully. You can delete 'test_sequences.fasta' if not needed.\n")
}
