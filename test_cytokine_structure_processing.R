# Test script for structure_processing chunk with actual cytokine data
library(bio3d)

# Create test directories
dir.create("structures", showWarnings = FALSE)
dir.create("alignments", showWarnings = FALSE)

# Define cytokine families (from the R markdown)
il6_family <- c("IL6", "IL11", "CNTF", "LIF", "OSM", "CTF1", "CLCF1", "IL27")
il12_family <- c("IL12A", "IL12B", "IL23A", "IL27A", "EBI3")

# Function to trim PDB structures (from the chunk)
trim_pdb_structure <- function(pdb_file, output_file = NULL) {
  if (!file.exists(pdb_file)) {
    cat("PDB file not found:", pdb_file, "\n")
    return(NULL)
  }

  tryCatch({
    # Read PDB file
    pdb <- read.pdb(pdb_file)
    cat("Original PDB:", basename(pdb_file), "- atoms:", nrow(pdb$atom), "\n")

    # Trim to protein only (removes waters, ligands, etc.)
    pdb_trimmed <- trim.pdb(pdb, "protein")
    cat("Trimmed PDB: atoms:", nrow(pdb_trimmed$atom), "\n")

    # Generate output filename if not provided
    if (is.null(output_file)) {
      output_file <- gsub("\\.pdb$", "_trimmed.pdb", pdb_file)
    }

    # Save trimmed structure
    write.pdb(pdb_trimmed, file = output_file)

    if (file.exists(output_file)) {
      cat("✓ Trimmed structure saved:", basename(output_file), "\n")
      return(output_file)
    } else {
      cat("✗ Failed to save trimmed structure\n")
      return(NULL)
    }

  }, error = function(e) {
    cat("Error trimming PDB:", e$message, "\n")
    return(NULL)
  })
}

# Mock pdb_data structure (simulating what would come from previous chunks)
pdb_data <- list(
  "IL6" = list(
    downloaded_files = c("structures/1p9m.pdb"),
    success_count = 1
  ),
  "IL11" = list(
    downloaded_files = c("structures/8DPT.pdb"),
    success_count = 1
  ),
  "LIF" = list(
    downloaded_files = c("structures/8D6A.pdb"),
    success_count = 1
  ),
  "OSM" = list(
    downloaded_files = c("structures/8V29.pdb"),
    success_count = 1
  ),
  "IL23A" = list(
    downloaded_files = c("structures/6WDQ.pdb"),
    success_count = 1
  ),
  "CNTF" = list(
    downloaded_files = c("structures/8D74.pdb"),
    success_count = 1
  ),
  "IL27A" = list(
    downloaded_files = c("structures/7U7N.pdb"),
    success_count = 1
  ),
  "CLCF1" = list(
    downloaded_files = c("structures/8D7H.pdb"),
    success_count = 1
  )
)

cat("Testing structure_processing chunk with cytokine data...\n")

# Process all downloaded PDB structures (from the chunk)
trimmed_structures <- list()

for (gene in names(pdb_data)) {
  if (pdb_data[[gene]]$success_count > 0) {
    cat("Processing structures for", gene, "...\n")

    gene_trimmed <- character()

    for (pdb_file in pdb_data[[gene]]$downloaded_files) {
      if (file.exists(pdb_file)) {
        trimmed_file <- trim_pdb_structure(pdb_file)

        if (!is.null(trimmed_file)) {
          gene_trimmed <- c(gene_trimmed, trimmed_file)
        }
      } else {
        cat("  File not found:", pdb_file, "\n")
      }
    }

    if (length(gene_trimmed) > 0) {
      trimmed_structures[[gene]] <- gene_trimmed
      cat("  ✓ Processed", length(gene_trimmed), "structures for", gene, "\n")
    }
  }
}

cat("Total trimmed structures:", sum(sapply(trimmed_structures, length)), "\n")

# Test the family separation logic (from the chunk)
il6_structures <- trimmed_structures[names(trimmed_structures) %in% il6_family]
il12_structures <- trimmed_structures[names(trimmed_structures) %in% il12_family]

cat("\nFamily separation results:\n")
cat("IL-6 family structures:", length(il6_structures), "genes\n")
for (gene in names(il6_structures)) {
  cat("  ", gene, ":", length(il6_structures[[gene]]), "structures\n")
}

cat("IL-12 family structures:", length(il12_structures), "genes\n")
for (gene in names(il12_structures)) {
  cat("  ", gene, ":", length(il12_structures[[gene]]), "structures\n")
}

# Test the file flattening logic (from the chunk)
il6_files <- sapply(il6_structures, function(x) x[1])
il12_files <- sapply(il12_structures, function(x) x[1])

cat("\nFlattened structure files:\n")
cat("IL-6 family files for alignment:\n")
for (i in seq_along(il6_files)) {
  cat("  ", names(il6_files)[i], ":", basename(il6_files[i]), "\n")
}

cat("IL-12 family files for alignment:\n")
for (i in seq_along(il12_files)) {
  cat("  ", names(il12_files)[i], ":", basename(il12_files[i]), "\n")
}

cat("\nStructure processing test completed successfully!\n")
cat("Summary:\n")
cat("- Genes processed:", length(pdb_data), "\n")
cat("- Structures trimmed:", sum(sapply(trimmed_structures, length)), "\n")
cat("- IL-6 family ready for alignment:", length(il6_files), "structures\n")
cat("- IL-12 family ready for alignment:", length(il12_files), "structures\n")
