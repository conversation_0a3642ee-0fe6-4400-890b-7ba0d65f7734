# Working PDB Structure Functions
# Tested and verified to work with bio3d package

library(bio3d)
library(httr)

# Function to search for known PDB structures for cytokines
# Since automated PDB search is complex, we'll use known structures
get_known_cytokine_pdbs <- function() {
  # Known PDB structures for cytokines (manually curated)
  # Format: "pdb_id:chain" or just "pdb_id" for all chains
  # All PDB IDs follow standard 4-character alphanumeric format
  # Chain IDs follow PDB conventions (single or multi-character)
  known_structures <- list(
    # IL-6-type family cytokines
    "IL6" = c("1p9m:B"),                       # Interleukin-6
    "IL11" = c("8dpt:B", "8dpt:E"),           # Interleukin-11
    "LIF" = c("8d6a:A"),                       # Leukemia inhibitory factor
    "OSM" = c("8v29:A"),                       # Oncostatin M
    "CNTF" = c("8d74:B"),                      # Ciliary neurotrophic factor
    "CLCF1" = c("8d7h:B", "8d7h:E"),          # Cardiotrophin-like cytokine

    # IL-12 family cytokine subunits
    "IL12A" = c("8xrp:A", "8xrp:E", "8xrp:I", "8xrp:M"),  # IL-12 p35 subunit
    "IL12B" = c("8xrp:B", "8xrp:F", "8xrp:J", "8xrp:N"),  # IL-12 p40 subunit (shared with IL-23)
    "IL23A" = c("6wdq:B"),                     # IL-23 p19 subunit
    "IL27A" = c("7u7n:D"),                     # IL-27 p28 subunit
    "EBI3" = c("7u7n:C")                       # EBI3 subunit (shared by IL-27 and IL-35)

    # Note: IL-27 is composed of IL27A + EBI3
    # Note: IL-35 is composed of IL12A + EBI3
    # Note: IL-12 is composed of IL12A + IL12B
    # Note: IL-23 is composed of IL23A + IL12B

    # Additional structures can be added following the same format
    # Ensure PDB IDs are 4-character alphanumeric (e.g., "1abc", "2def")
    # Chain IDs should match actual chains in the PDB structure

    # Example for future additions:
    # "NEW_CYTOKINE" = c("1abc:A", "2def:B", "3ghi")
  )

  return(known_structures)
}

# Function to parse PDB ID and chain from string
# Follows PDB conventions: 4-character alphanumeric PDB IDs, flexible chain IDs
parse_pdb_chain <- function(pdb_string) {
  if (grepl(":", pdb_string)) {
    parts <- strsplit(pdb_string, ":")[[1]]
    pdb_id <- parts[1]
    chain_id <- parts[2]

    # Validate PDB ID format (4 characters, alphanumeric)
    if (!grepl("^[0-9A-Za-z]{4}$", pdb_id)) {
      warning("PDB ID '", pdb_id, "' does not follow standard 4-character format")
    }

    # Validate chain ID (1-4 characters, alphanumeric)
    if (!grepl("^[A-Za-z0-9]{1,4}$", chain_id)) {
      warning("Chain ID '", chain_id, "' may not be valid (should be 1-4 alphanumeric characters)")
    }

    return(list(pdb_id = toupper(pdb_id), chain = chain_id))
  } else {
    pdb_id <- pdb_string

    # Validate PDB ID format
    if (!grepl("^[0-9A-Za-z]{4}$", pdb_id)) {
      warning("PDB ID '", pdb_id, "' does not follow standard 4-character format")
    }

    return(list(pdb_id = toupper(pdb_id), chain = NULL))
  }
}

# Function to get detailed chain information from a PDB file
# Follows PDB conventions for chain identification
get_pdb_chain_info <- function(pdb_file) {
  if (!file.exists(pdb_file)) {
    return(NULL)
  }

  tryCatch({
    pdb <- read.pdb(pdb_file)

    # Get unique chains
    chains <- unique(pdb$atom$chain)

    # Get chain information
    chain_info <- list()
    for (chain in chains) {
      chain_atoms <- pdb$atom[pdb$atom$chain == chain, ]

      # Count different atom types
      protein_atoms <- sum(chain_atoms$type == "ATOM")
      hetatm_atoms <- sum(chain_atoms$type == "HETATM")

      # Get residue information
      residues <- unique(chain_atoms$resid)

      chain_info[[chain]] <- list(
        chain_id = chain,
        total_atoms = nrow(chain_atoms),
        protein_atoms = protein_atoms,
        hetatm_atoms = hetatm_atoms,
        residue_count = length(residues),
        residues = residues[1:min(5, length(residues))]  # First 5 residues
      )
    }

    return(list(
      pdb_file = pdb_file,
      total_chains = length(chains),
      chains = chains,
      chain_details = chain_info
    ))

  }, error = function(e) {
    warning("Error reading PDB file ", pdb_file, ": ", e$message)
    return(NULL)
  })
}

# Function to validate chain specification against actual PDB content
validate_chain_specification <- function(pdb_file, specified_chain) {
  chain_info <- get_pdb_chain_info(pdb_file)

  if (is.null(chain_info)) {
    return(list(valid = FALSE, message = "Could not read PDB file"))
  }

  if (is.null(specified_chain)) {
    return(list(valid = TRUE, message = "No chain specified - using all chains"))
  }

  if (specified_chain %in% chain_info$chains) {
    chain_details <- chain_info$chain_details[[specified_chain]]
    return(list(
      valid = TRUE,
      message = paste("Chain", specified_chain, "found with",
                     chain_details$protein_atoms, "protein atoms")
    ))
  } else {
    return(list(
      valid = FALSE,
      message = paste("Chain", specified_chain, "not found. Available chains:",
                     paste(chain_info$chains, collapse = ", "))
    ))
  }
}

# Function to download PDB structures for a gene with chain specification
download_pdb_structures <- function(gene_name, output_dir = "structures") {
  # Create output directory
  dir.create(output_dir, showWarnings = FALSE)

  # Get known PDB IDs for this gene
  known_pdbs <- get_known_cytokine_pdbs()

  if (gene_name %in% names(known_pdbs)) {
    pdb_entries <- known_pdbs[[gene_name]]
    cat("Found", length(pdb_entries), "known PDB structures for", gene_name, "\n")

    downloaded_files <- character()
    chain_info <- list()

    for (pdb_entry in pdb_entries) {
      # Parse PDB ID and chain
      parsed <- parse_pdb_chain(pdb_entry)
      pdb_id <- parsed$pdb_id
      chain <- parsed$chain

      cat("  Downloading", pdb_id)
      if (!is.null(chain)) {
        cat(" (chain", chain, ")")
      }
      cat("...")

      tryCatch({
        # Create unique filename that includes chain information
        if (!is.null(chain)) {
          # Use chain-specific filename to avoid overwriting
          base_filename <- paste0(tolower(pdb_id), "_chain", chain, ".pdb")
          pdb_file <- file.path(output_dir, base_filename)

          # Download to temporary location first
          temp_file <- get.pdb(pdb_id, path = output_dir)

          # Copy to chain-specific filename
          if (file.exists(temp_file)) {
            file.copy(temp_file, pdb_file, overwrite = TRUE)
          }
        } else {
          # No specific chain, use standard filename
          pdb_file <- get.pdb(pdb_id, path = output_dir)
        }

        if (file.exists(pdb_file)) {
          # Validate chain specification against actual PDB content
          validation <- validate_chain_specification(pdb_file, chain)

          if (validation$valid) {
            downloaded_files <- c(downloaded_files, pdb_file)
            chain_info[[pdb_file]] <- list(
              pdb_id = pdb_id,
              chain = chain,
              entry = pdb_entry,
              validation = validation$message,
              original_filename = basename(pdb_file)
            )
            cat(" ✓ Success")
            if (!is.null(chain)) {
              cat(" (saved as", basename(pdb_file), ",", validation$message, ")")
            }
            cat("\n")
          } else {
            cat(" ✗ Chain validation failed:", validation$message, "\n")
          }
        } else {
          cat(" ✗ Download failed\n")
        }

      }, error = function(e) {
        cat(" ✗ Error:", e$message, "\n")
      })

      Sys.sleep(0.5)  # Be respectful to PDB servers
    }

    return(list(
      gene = gene_name,
      pdb_entries = pdb_entries,
      downloaded_files = downloaded_files,
      chain_info = chain_info,
      success_count = length(downloaded_files)
    ))

  } else {
    cat("No known PDB structures for", gene_name, "\n")
    return(NULL)
  }
}

# Function to trim PDB structures (remove waters, ligands, etc.) with chain selection
trim_pdb_structure <- function(pdb_file, output_file = NULL, chain = NULL) {
  if (!file.exists(pdb_file)) {
    cat("PDB file not found:", pdb_file, "\n")
    return(NULL)
  }

  tryCatch({
    # Read PDB file
    pdb <- read.pdb(pdb_file)
    cat("Original PDB:", basename(pdb_file), "- atoms:", nrow(pdb$atom))

    # Show available chains
    available_chains <- unique(pdb$atom$chain)
    cat(", chains:", paste(available_chains, collapse = ", "), "\n")

    # Select specific chain if specified
    if (!is.null(chain)) {
      if (chain %in% available_chains) {
        cat("Selecting chain", chain, "...\n")
        pdb <- trim.pdb(pdb, chain = chain)
        cat("Chain-selected PDB: atoms:", nrow(pdb$atom), "\n")
      } else {
        cat("Warning: Chain", chain, "not found. Available chains:", paste(available_chains, collapse = ", "), "\n")
        cat("Using all chains...\n")
      }
    }

    # Trim to protein only (removes waters, ligands, etc.)
    pdb_trimmed <- trim.pdb(pdb, "protein")
    cat("Trimmed PDB: atoms:", nrow(pdb_trimmed$atom), "\n")

    # Generate output filename if not provided
    if (is.null(output_file)) {
      base_name <- gsub("\\.pdb$", "", pdb_file)
      if (!is.null(chain)) {
        output_file <- paste0(base_name, "_chain", chain, "_trimmed.pdb")
      } else {
        output_file <- paste0(base_name, "_trimmed.pdb")
      }
    }

    # Save trimmed structure
    write.pdb(pdb_trimmed, file = output_file)

    if (file.exists(output_file)) {
      cat("✓ Trimmed structure saved:", basename(output_file), "\n")
      return(output_file)
    } else {
      cat("✗ Failed to save trimmed structure\n")
      return(NULL)
    }

  }, error = function(e) {
    cat("Error trimming PDB:", e$message, "\n")
    return(NULL)
  })
}

# Function to run structure-based alignment using bio3d's pdbaln
# This is an alternative to MUSTANG that doesn't require external software
run_structure_alignment <- function(pdb_files, output_file = "structure_alignment.fasta") {
  if (length(pdb_files) < 2) {
    cat("Need at least 2 PDB files for alignment\n")
    return(NULL)
  }

  cat("Running structure-based alignment with", length(pdb_files), "structures...\n")

  tryCatch({
    # Read all PDB files
    pdbs <- list()
    for (i in seq_along(pdb_files)) {
      if (file.exists(pdb_files[i])) {
        pdbs[[i]] <- read.pdb(pdb_files[i])
        cat("  Read:", basename(pdb_files[i]), "\n")
      } else {
        cat("  Missing:", pdb_files[i], "\n")
      }
    }

    if (length(pdbs) < 2) {
      cat("Not enough valid PDB files for alignment\n")
      return(NULL)
    }

    # Use pdbaln for structure-based alignment
    cat("Running pdbaln alignment...\n")
    alignment_result <- pdbaln(pdbs, fit = TRUE)

    # Extract aligned sequences
    aligned_seqs <- alignment_result$ali

    # Create FASTA output
    fasta_content <- character()
    for (i in 1:nrow(aligned_seqs)) {
      pdb_name <- basename(pdb_files[i])
      pdb_id <- gsub("\\.pdb.*", "", pdb_name)

      fasta_content <- c(fasta_content,
                        paste0(">", pdb_id),
                        paste(aligned_seqs[i, ], collapse = ""))
    }

    # Write FASTA file
    writeLines(fasta_content, output_file)

    cat("✓ Structure alignment completed:", output_file, "\n")
    return(list(
      alignment = alignment_result,
      output_file = output_file,
      n_structures = length(pdbs)
    ))

  }, error = function(e) {
    cat("Error in structure alignment:", e$message, "\n")
    return(NULL)
  })
}

# Alternative MUSTANG function that checks if MUSTANG is installed
run_mustang_alignment <- function(pdb_files, output_file = "mustang_alignment.fasta") {
  if (length(pdb_files) < 2) {
    cat("Need at least 2 PDB files for MUSTANG alignment\n")
    return(NULL)
  }

  # Check if MUSTANG is available
  mustang_available <- tryCatch({
    system("mustang --version", ignore.stderr = TRUE, ignore.stdout = TRUE)
    TRUE
  }, error = function(e) {
    FALSE
  })

  if (!mustang_available) {
    cat("MUSTANG not found in system PATH\n")
    cat("Falling back to bio3d structure alignment...\n")
    return(run_structure_alignment(pdb_files, output_file))
  }

  tryCatch({
    # Use bio3d's mustang wrapper
    mustang_result <- mustang(pdb_files, outfile = output_file)

    cat("✓ MUSTANG alignment completed:", output_file, "\n")
    return(mustang_result)

  }, error = function(e) {
    cat("Error in MUSTANG alignment:", e$message, "\n")
    cat("Falling back to bio3d structure alignment...\n")
    return(run_structure_alignment(pdb_files, output_file))
  })
}

# Function to batch process PDB structures for multiple genes
batch_process_structures <- function(gene_list, output_dir = "structures") {
  results <- list()

  cat("Processing PDB structures for", length(gene_list), "genes...\n")

  for (gene in gene_list) {
    cat("\n--- Processing", gene, "---\n")

    # Download structures
    download_result <- download_pdb_structures(gene, output_dir)

    if (!is.null(download_result) && download_result$success_count > 0) {
      # Trim structures with chain information
      trimmed_files <- character()

      for (pdb_file in download_result$downloaded_files) {
        # Get chain information for this file
        chain_info <- download_result$chain_info[[pdb_file]]
        chain <- if (!is.null(chain_info)) chain_info$chain else NULL

        trimmed_file <- trim_pdb_structure(pdb_file, chain = chain)
        if (!is.null(trimmed_file)) {
          trimmed_files <- c(trimmed_files, trimmed_file)
        }
      }

      results[[gene]] <- list(
        pdb_entries = download_result$pdb_entries,
        downloaded_files = download_result$downloaded_files,
        chain_info = download_result$chain_info,
        trimmed_files = trimmed_files,
        success_count = length(trimmed_files)
      )

      cat("✓ Processed", length(trimmed_files), "structures for", gene, "\n")
    } else {
      cat("✗ No structures processed for", gene, "\n")
    }
  }

  return(results)
}

# Function to create summary of PDB processing results
create_pdb_summary <- function(pdb_results) {
  if (length(pdb_results) == 0) {
    return(data.frame())
  }

  summary_df <- do.call(rbind, lapply(names(pdb_results), function(gene) {
    result <- pdb_results[[gene]]
    data.frame(
      Gene = gene,
      PDB_Count = length(result$pdb_entries),
      Downloaded = length(result$downloaded_files),
      Trimmed = length(result$trimmed_files),
      PDB_Entries = paste(result$pdb_entries, collapse = ", "),
      stringsAsFactors = FALSE
    )
  }))

  return(summary_df)
}

cat("PDB structure functions loaded successfully!\n")
cat("Main functions:\n")
cat("  - download_pdb_structures(gene_name): Download known PDB structures\n")
cat("  - trim_pdb_structure(pdb_file): Remove waters and ligands\n")
cat("  - run_structure_alignment(pdb_files): Structure-based alignment\n")
cat("  - run_mustang_alignment(pdb_files): MUSTANG alignment (with fallback)\n")
cat("  - batch_process_structures(gene_list): Process multiple genes\n")
cat("  - get_known_cytokine_pdbs(): Get list of known cytokine PDB structures\n")
